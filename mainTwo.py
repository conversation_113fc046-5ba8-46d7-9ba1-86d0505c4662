from sklearn.model_selection import train_test_split
from rdkit import Chem
from rdkit.Chem.rdFingerprintGenerator import GetMorganGenerator, GetAtomPairGenerator, GetTopologicalTorsionGenerator
from rdkit.Chem import MACCSkeys
from rdkit.Chem import Descriptors
from sklearn.model_selection import KFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error
from rdkit import Chem
from rdkit.Chem import AllChem, MACCSkeys, Descriptors
from rdkit.Chem.rdFingerprintGenerator import GetMorganGenerator, GetAtomPairGenerator, GetTopologicalTorsionGenerator
from catboost import CatBoostRegressor
from sklearn.metrics import mean_absolute_error
from sklearn.feature_selection import VarianceThreshold

import numpy as np
import random
import xgboost as xgb
from xgboost import XGBRegressor
import lightgbm as lgb
from sklearn.svm import SVR
from sklearn.ensemble import HistGradientBoostingRegressor
from sklearn.ensemble import ExtraTreesRegressor
from catboost import CatBoostRegressor
from sklearn.ensemble import RandomForestRegressor
from sklearn.mixture import GaussianMixture
from sklearn.model_selection import KFold
from sklearn.linear_model import Ridge,Lasso
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import make_pipeline
from rdkit.Chem import Descriptors, MACCSkeys
from rdkit.Chem.rdMolDescriptors import CalcTPSA, CalcNumRotatableBonds
from rdkit.Chem.Descriptors import MolWt, MolLogP
from rdkit.Chem.rdFingerprintGenerator import GetMorganGenerator, GetAtomPairGenerator, GetTopologicalTorsionGenerator
import networkx as nx

from .filters import *
from .config import *

import warnings
warnings.filterwarnings("ignore")


import numpy as np # linear algebra
import pandas as pd # data processing, CSV file I/O (e.g. pd.read_csv)
import os
from rdkit import Chem
from rdkit.Chem import Descriptors, rdMolDescriptors, AllChem, Fragments, Lipinski
from rdkit.Chem import rdmolops
# Data paths

root = "./data"
BASE_PATH = f'{root}/neurips-open-polymer-prediction-2025/'
RDKIT_AVAILABLE = True
TARGETS = ['Tg', 'FFV', 'Tc', 'Density', 'Rg']
def get_canonical_smiles(smiles):
        """Convert SMILES to canonical form for consistency"""
        if not RDKIT_AVAILABLE:
            return smiles
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol:
                return Chem.MolToSmiles(mol, canonical=True)
        except:
            pass
        return smiles
#Cell 3: Robust Data Loading with Complete R-Group Filtering
"""
Load competition data with complete filtering of problematic polymer notation
"""

print("📂 Loading competition data...")
train = pd.read_csv(BASE_PATH + 'train.csv')
test = pd.read_csv(BASE_PATH + 'test.csv')

print(f"   Training samples: {len(train)}")
print(f"   Test samples: {len(test)}")

####

# Clean and validate all SMILES
print("🔄 Cleaning and validating SMILES...")
train['SMILES'] = train['SMILES'].apply(clean_and_validate_smiles)
test['SMILES'] = test['SMILES'].apply(clean_and_validate_smiles)

# Remove invalid SMILES
invalid_train = train['SMILES'].isnull().sum()
invalid_test = test['SMILES'].isnull().sum()

print(f"   Removed {invalid_train} invalid SMILES from training data")
print(f"   Removed {invalid_test} invalid SMILES from test data")

train = train[train['SMILES'].notnull()].reset_index(drop=True)
test = test[test['SMILES'].notnull()].reset_index(drop=True)

print(f"   Final training samples: {len(train)}")
print(f"   Final test samples: {len(test)}")

def add_extra_data_clean(df_train, df_extra, target):
    """Add external data with thorough SMILES cleaning"""
    n_samples_before = len(df_train[df_train[target].notnull()])
    
    print(f"      Processing {len(df_extra)} {target} samples...")
    
    # Clean external SMILES
    df_extra['SMILES'] = df_extra['SMILES'].apply(clean_and_validate_smiles)
    
    # Remove invalid SMILES and missing targets
    before_filter = len(df_extra)
    df_extra = df_extra[df_extra['SMILES'].notnull()]
    df_extra = df_extra.dropna(subset=[target])
    after_filter = len(df_extra)
    
    print(f"      Kept {after_filter}/{before_filter} valid samples")
    
    if len(df_extra) == 0:
        print(f"      No valid data remaining for {target}")
        return df_train
    
    # Group by canonical SMILES and average duplicates
    df_extra = df_extra.groupby('SMILES', as_index=False)[target].mean()
    
    cross_smiles = set(df_extra['SMILES']) & set(df_train['SMILES'])
    unique_smiles_extra = set(df_extra['SMILES']) - set(df_train['SMILES'])

    # Fill missing values
    filled_count = 0
    for smile in df_train[df_train[target].isnull()]['SMILES'].tolist():
        if smile in cross_smiles:
            df_train.loc[df_train['SMILES']==smile, target] = \
                df_extra[df_extra['SMILES']==smile][target].values[0]
            filled_count += 1
    
    # Add unique SMILES
    extra_to_add = df_extra[df_extra['SMILES'].isin(unique_smiles_extra)].copy()
    if len(extra_to_add) > 0:
        for col in TARGETS:
            if col not in extra_to_add.columns:
                extra_to_add[col] = np.nan
        
        extra_to_add = extra_to_add[['SMILES'] + TARGETS]
        df_train = pd.concat([df_train, extra_to_add], axis=0, ignore_index=True)

    n_samples_after = len(df_train[df_train[target].notnull()])
    print(f'      {target}: +{n_samples_after-n_samples_before} samples, +{len(unique_smiles_extra)} unique SMILES')
    return df_train

# Load external datasets with robust error handling
print("\n📂 Loading external datasets...")

external_datasets = []


# Load each dataset
external_datasets = safe_load_dataset(
    f'{root}/tc-smiles/Tc_SMILES.csv',
    'Tc',
    lambda df: df.rename(columns={'TC_mean': 'Tc'}),
    'Tc data',
    external_datasets
)

external_datasets = safe_load_dataset(
    f'{root}/tg-smiles-pid-polymer-class/TgSS_enriched_cleaned.csv',
    'Tg', 
    lambda df: df[['SMILES', 'Tg']] if 'Tg' in df.columns else df,
    'TgSS enriched data',
    external_datasets
)

external_datasets = safe_load_dataset(
    f'{root}/smiles-extra-data/JCIM_sup_bigsmiles.csv',
    'Tg',
    lambda df: df[['SMILES', 'Tg (C)']].rename(columns={'Tg (C)': 'Tg'}),
    'JCIM Tg data',
    external_datasets
)

external_datasets = safe_load_dataset(
    f'{root}/smiles-extra-data/data_tg3.xlsx',
    'Tg',
    lambda df: df.rename(columns={'Tg [K]': 'Tg'}).assign(Tg=lambda x: x['Tg'] - 273.15),
    'Xlsx Tg data',
    external_datasets
)

external_datasets = safe_load_dataset(
    f'{root}/smiles-extra-data/data_dnst1.xlsx',
    'Density',
    lambda df: df.rename(columns={'density(g/cm3)': 'Density'})[['SMILES', 'Density']]
                .query('SMILES.notnull() and Density.notnull() and Density != "nylon"')
                .assign(Density=lambda x: x['Density'].astype(float) - 0.118),
    'Density data',
    external_datasets
)

external_datasets = safe_load_dataset(
    f'{root}/neurips-open-polymer-prediction-2025/train_supplement/dataset4.csv',
    'FFV', 
    lambda df: df[['SMILES', 'FFV']] if 'FFV' in df.columns else df,
    'dataset 4',
    external_datasets
)

external_datasets = safe_load_dataset(
    f'{root}/neurips-open-polymer-prediction-2025/train_supplement/dataset1.csv',
    'Tc', 
    lambda df: df[['SMILES', 'TC_mean']].rename(columns={'TC_mean': 'Tc'}),
    'dataset 1',
    external_datasets
)

external_datasets = safe_load_dataset(
    f'{root}/neurips-open-polymer-prediction-2025/train_supplement/dataset3.csv',
    'Tg', 
    lambda df: df[['SMILES', 'Tg']].rename(columns={'Tg': 'Tg'}),
    'dataset 3',
    external_datasets
)


# Integrate external data
print("\n🔄 Integrating external data...")
train_extended = train[['SMILES'] + TARGETS].copy()

for target, dataset in external_datasets:
    print(f"   Processing {target} data...")
    train_extended = add_extra_data_clean(train_extended, dataset, target)

print(f"\n📊 Final training data:")
print(f"   Original samples: {len(train)}")
print(f"   Extended samples: {len(train_extended)}")
print(f"   Gain: +{len(train_extended) - len(train)} samples")

for target in TARGETS:
    count = train_extended[target].notna().sum()
    original_count = train[target].notna().sum() if target in train.columns else 0
    gain = count - original_count
    print(f"   {target}: {count:,} samples (+{gain})")

print(f"\n✅ Data integration complete with clean SMILES!")



train_df=train_extended
test_df=test
subtables = separate_subtables(train_df)

test_smiles = test_df['SMILES'].tolist()
test_ids = test_df['id'].values
labels = ["Tg", "FFV", "Tc", "Density", "Rg"]
#labels = ['Tc']

output_df = pd.DataFrame({
	'id': test_ids
})

data_per_label = {}
test_data_per_label = {}

for label in labels:
    print(f"Processing label: {label}")
    print(subtables[label].head())
    print(subtables[label].shape)
    original_smiles = subtables[label]['SMILES'].tolist()
    original_labels = subtables[label][label].values
    
    original_smiles, original_labels = augment_smiles_dataset(original_smiles, original_labels, num_augments=1)
    fingerprints, descriptors, valid_smiles, invalid_indices = smiles_to_combined_fingerprints_with_descriptors(original_smiles, filters[label], radius=2, n_bits=128)
    # descriptors, valid_smiles, invalid_indices\
    #	 =smiles_to_descriptors_with_fingerprints(original_smiles, radius=2, n_bits=128)
    
    X=pd.DataFrame(descriptors)
    y = np.delete(original_labels, invalid_indices)
    
    # pd.DataFrame(X).to_csv(f"./mats/{label}.csv")
    # pd.DataFrame(y).to_csv(f"./mats/{label}label.csv", header=None)
    
    # binned = pd.qcut(y, q=10, labels=False, duplicates='drop')
    # pd.DataFrame(binned).to_csv(f"./mats/{label}integerlabel.csv", header=None, index=False)
    X = X.filter(filters[label])
    # Convert fingerprints array to DataFrame
    fp_df = pd.DataFrame(fingerprints, columns=[f'FP_{i}' for i in range(fingerprints.shape[1])])
    
    print(fp_df.shape)
    # Reset index to align with X
    fp_df.reset_index(drop=True, inplace=True)
    X.reset_index(drop=True, inplace=True)
    # Concatenate descriptors and fingerprints
    X = pd.concat([X, fp_df], axis=1)
    
    print(f"After concat: {X.shape}")
    
    # Set the variance threshold
    threshold = 0.01
    
    # Apply VarianceThreshold
    selector = VarianceThreshold(threshold=threshold)
    
    X = selector.fit_transform(X)
    
    print(f"After variance cut: {X.shape}")
    
    
    n_samples = 2000
    
    data_per_label[label] = augment_dataset(X, y, n_samples=n_samples)
    print(f"After augment cut: {data_per_label[label].shape}")
    
    fingerprints, descriptors, valid_smiles, invalid_indices = smiles_to_combined_fingerprints_with_descriptors(test_smiles, filters[label], radius=2, n_bits=128)
    
    test = pd.DataFrame(descriptors)
    
    test = test.filter(filters[label])
    
    # Convert fingerprints array to DataFrame
    fp_df = pd.DataFrame(fingerprints, columns=[f'FP_{i}' for i in range(fingerprints.shape[1])])
    
    # Reset index to align with X
    fp_df.reset_index(drop=True, inplace=True)
    test.reset_index(drop=True, inplace=True)
    
    # Concatenate descriptors and fingerprints
    test = pd.concat([test, fp_df], axis=1)
    test = selector.transform(test)
    
    print(test.shape)
    test_data_per_label[label] = test

test_data_per_label['Tg'] = pd.DataFrame(test_data_per_label['Tg'])
test_data_per_label['FFV'] = pd.DataFrame(test_data_per_label['FFV'])
test_data_per_label['Tc'] = pd.DataFrame(test_data_per_label['Tc'])
test_data_per_label['Density'] = pd.DataFrame(test_data_per_label['Density'])
test_data_per_label['Rg'] = pd.DataFrame(test_data_per_label['Rg'])
data_per_label['Tg'].rename(columns={'Target': 'Tg'}, inplace=True)
data_per_label['FFV'].rename(columns={'Target': 'FFV'}, inplace=True)
data_per_label['Tc'].rename(columns={'Target': 'Tc'}, inplace=True)
data_per_label['Density'].rename(columns={'Target': 'Density'}, inplace=True)
data_per_label['Rg'].rename(columns={'Target': 'Rg'}, inplace=True)

gbdt_predictions_df = pd.DataFrame({'id': test_df['id']})
######################Tg Training
TARGET_VARIABLES = ['Tg']
RANDOM_STATE = 42
gbdt_oof_df = {}
mae_scores = {}
all_importances = {}

for target in TARGET_VARIABLES:
    X_test = test_data_per_label[target]
    X = data_per_label[target]
    print(f"  Training for {target}...")
    
    y = data_per_label[target][target].dropna()
    X_subset = X.loc[y.index]
    X_test = X_test.to_numpy()
    cols = X_subset.drop(columns=[target]).columns
    print(f"Size for data is {X_subset.shape} and features are {len(cols)}")
    
    X_train_fold, X_val_fold, y_train_fold, y_val_fold = train_test_split(
        X_subset.drop(columns=[target]), y, test_size=0.2, random_state=10
    )
    
    X_train_fold = X_train_fold.to_numpy()
    X_val_fold = X_val_fold.to_numpy()
    y_train_fold = y_train_fold.to_numpy()
    y_val_fold = y_val_fold.to_numpy()

    # XGBoost
    if target == "Tg":
        xgb_model = XGBRegressor(n_estimators= 2173, learning_rate= 0.0672418745539774, max_depth= 6, reg_lambda= 5.545520219149715, random_state = 4)
    elif target == "Rg":
        xgb_model = XGBRegressor(n_estimators= 520, learning_rate= 0.07324113948440986, max_depth= 5, reg_lambda=0.9717380315982088, random_state = 4)
    elif target == "FFV":
        xgb_model = XGBRegressor(n_estimators= 2202, learning_rate= 0.07220580588586338, max_depth= 4, reg_lambda= 2.8872976032666493, random_state = 4)
    elif target == "Tc":
        xgb_model = XGBRegressor(n_estimators= 1488, learning_rate= 0.010456188013762864, max_depth= 5, reg_lambda= 9.970345982204618, random_state = 4)
    elif target == "Density":
        xgb_model = XGBRegressor(n_estimators= 1958, learning_rate= 0.10955287548172478, max_depth= 5, reg_lambda= 3.074470087965767, random_state = 4)

    xgb_model.fit(X_train_fold, y_train_fold, eval_set=[(X_val_fold, y_val_fold)], verbose=False)
    xgb_oof_preds = xgb_model.predict(X_val_fold)
    test_preds_xgb = xgb_model.predict(X_test)
    importance_values = xgb_model.feature_importances_
    importance_dict = dict(zip(cols, importance_values))
    all_importances[target] = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)

    # LightGBM
  
    lgb_model = lgb.LGBMRegressor(n_estimators = 46820,max_depth= 8,num_leaves = 146, min_child_samples= 38,
                                  min_gain_to_split = 0.19026687141998727,learning_rate = 0.023300052655922467,
                                  subsample= 0.917793716129109,colsample_bytree = 0.6754104432113612,
                                  reg_lambda =2.8777986886787335,reg_alpha = 4.066237690162047e-06,verbose = -1
                                 )
    lgb_model.fit(X_train_fold, y_train_fold,
                  eval_set=[(X_val_fold, y_val_fold)],
                  callbacks=[lgb.early_stopping(stopping_rounds=30, verbose=False)])
    oof_preds_lgb = lgb_model.predict(X_val_fold)
    test_preds_lgb = lgb_model.predict(X_test)

    # Extra Trees
    et_model = ExtraTreesRegressor()
    et_model.fit(X_train_fold, y_train_fold)
    oof_preds_et = et_model.predict(X_val_fold)
    test_preds_et = et_model.predict(X_test)

    # # CatBoost
    # cat_model = CatBoostRegressor(verbose=0)
    # cat_model.fit(X_train_fold, y_train_fold, eval_set=(X_val_fold, y_val_fold))
    # oof_preds_cat = cat_model.predict(X_val_fold)
    # test_preds_cat = cat_model.predict(X_test)


    # # Lasso
    # lasso_model = Lasso()
    # lasso_model.fit(X_train_fold, y_train_fold)
    # oof_preds_lasso = lasso_model.predict(X_val_fold)
    # test_preds_lasso = lasso_model.predict(X_test)

    # # # Random Forest
    # rff_model = RandomForestRegressor(random_state = 42)
    # rff_model.fit(X_train_fold, y_train_fold)
    # oof_preds_rff = rff_model.predict(X_val_fold)
    # test_preds_rff = rff_model.predict(X_test)

    # rff_model = RandomForestRegressor(random_state = 42)
    # rff_model.fit(X_train_fold, y_train_fold)
    # oof_preds_rff1 = rff_model.predict(X_val_fold)
    # test_preds_rff1 = rff_model.predict(X_test)

    # rff_model = RandomForestRegressor(random_state = 21)
    # rff_model.fit(X_train_fold, y_train_fold)
    # oof_preds_rff2 = rff_model.predict(X_val_fold)
    # test_preds_rff2 = rff_model.predict(X_test)
    
    # Averaging predictions
    val_preds_all = np.vstack([
        xgb_oof_preds,
        oof_preds_lgb,
        oof_preds_et,
        # oof_preds_cat,
        # oof_preds_lasso,
        # oof_preds_rff,
        #oof_preds_rff1,
        # oof_preds_rff2
    ])
    final_oof_preds = np.mean(val_preds_all, axis=0)

    test_preds_all = np.vstack([
        test_preds_xgb,
        test_preds_lgb,
        test_preds_et,
        # test_preds_cat,
        # test_preds_lasso,
        # test_preds_rff,
        #test_preds_rff1,
        # test_preds_rff2
    ])
    final_test_preds = np.mean(test_preds_all, axis=0)

    # Save predictions
    gbdt_predictions_df[target] = final_test_preds
    gbdt_oof_df[target] = final_oof_preds

    # MAE Computation
    mae = mean_absolute_error(y_val_fold, final_oof_preds)
    mae_scores[target] = mae
    print(f"    MAE on validation set for {target}: {mae:.4f}")

########################FFV Training
TARGET_VARIABLES = ['FFV']
RANDOM_STATE = 42
gbdt_oof_df = {}
mae_scores = {}
all_importances = {}

for target in TARGET_VARIABLES:
    X_test = test_data_per_label[target]
    X = data_per_label[target]
    print(f"  Training for {target}...")
    
    y = data_per_label[target][target].dropna()
    X_subset = X.loc[y.index]
    X_test = X_test.to_numpy()
    cols = X_subset.drop(columns=[target]).columns
    print(f"Size for data is {X_subset.shape} and features are {len(cols)}")
    
    X_train_fold, X_val_fold, y_train_fold, y_val_fold = train_test_split(
        X_subset.drop(columns=[target]), y, test_size=0.2, random_state=10
    )
    
    X_train_fold = X_train_fold.to_numpy()
    X_val_fold = X_val_fold.to_numpy()
    y_train_fold = y_train_fold.to_numpy()
    y_val_fold = y_val_fold.to_numpy()

    # XGBoost

    xgb_model = XGBRegressor(n_estimators= 2202, learning_rate= 0.07220580588586338, max_depth= 4, reg_lambda= 2.8872976032666493, random_state = 4)
   
    xgb_model.fit(X_train_fold, y_train_fold, eval_set=[(X_val_fold, y_val_fold)], verbose=False)
    xgb_oof_preds = xgb_model.predict(X_val_fold)
    test_preds_xgb = xgb_model.predict(X_test)
    importance_values = xgb_model.feature_importances_
    importance_dict = dict(zip(cols, importance_values))
    all_importances[target] = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)

    # #LightGBM

    """n_estimators = 46820,max_depth= 8,num_leaves = 146, min_child_samples= 38,
                                  min_gain_to_split = 0.19026687141998727,learning_rate = 0.023300052655922467,
                                  subsample= 0.917793716129109,colsample_bytree = 0.6754104432113612,
                                  reg_lambda =2.8777986886787335,reg_alpha = 4.066237690162047e-06,verbose = -1
                                 """
  
    # lgb_model = lgb.LGBMRegressor()
    # lgb_model.fit(X_train_fold, y_train_fold,
    #               eval_set=[(X_val_fold, y_val_fold)],
    #               callbacks=[lgb.early_stopping(stopping_rounds=30, verbose=False)])
    # oof_preds_lgb = lgb_model.predict(X_val_fold)
    # test_preds_lgb = lgb_model.predict(X_test)

    # Extra Trees
    # et_model = ExtraTreesRegressor()
    # et_model.fit(X_train_fold, y_train_fold)
    # oof_preds_et = et_model.predict(X_val_fold)
    # test_preds_et = et_model.predict(X_test)

    # # CatBoost
    # cat_model = CatBoostRegressor(verbose=0)
    # cat_model.fit(X_train_fold, y_train_fold, eval_set=(X_val_fold, y_val_fold))
    # oof_preds_cat = cat_model.predict(X_val_fold)
    # test_preds_cat = cat_model.predict(X_test)


    # # Lasso
    # lasso_model = Lasso()
    # lasso_model.fit(X_train_fold, y_train_fold)
    # oof_preds_lasso = lasso_model.predict(X_val_fold)
    # test_preds_lasso = lasso_model.predict(X_test)

    # # Random Forest
    rff_model = RandomForestRegressor(random_state = 42)
    rff_model.fit(X_train_fold, y_train_fold)
    oof_preds_rff = rff_model.predict(X_val_fold)
    test_preds_rff = rff_model.predict(X_test)

    # rff_model = RandomForestRegressor(random_state = 42)
    # rff_model.fit(X_train_fold, y_train_fold)
    # oof_preds_rff1 = rff_model.predict(X_val_fold)
    # test_preds_rff1 = rff_model.predict(X_test)

    # rff_model = RandomForestRegressor(random_state = 21)
    # rff_model.fit(X_train_fold, y_train_fold)
    # oof_preds_rff2 = rff_model.predict(X_val_fold)
    # test_preds_rff2 = rff_model.predict(X_test)
    
    # Averaging predictions
    val_preds_all = np.vstack([
        xgb_oof_preds,
        # oof_preds_lgb,
        # oof_preds_et,
        # oof_preds_cat,
        # oof_preds_lasso,
        oof_preds_rff,
        #oof_preds_rff1,
        # oof_preds_rff2
    ])
    final_oof_preds = np.mean(val_preds_all, axis=0)

    test_preds_all = np.vstack([
        test_preds_xgb,
        # test_preds_lgb,
        # test_preds_et,
        # test_preds_cat,
        # test_preds_lasso,
        test_preds_rff,
        #test_preds_rff1,
        # test_preds_rff2
    ])
    final_test_preds = np.mean(test_preds_all, axis=0)

    # Save predictions
    gbdt_predictions_df[target] = final_test_preds
    gbdt_oof_df[target] = final_oof_preds

    # MAE Computation
    mae = mean_absolute_error(y_val_fold, final_oof_preds)
    mae_scores[target] = mae
    print(f"    MAE on validation set for {target}: {mae:.4f}")


########################Tc Training
TARGET_VARIABLES = [ "Tc"]
RANDOM_STATE = 42
gbdt_oof_df = {}
mae_scores = {}
all_importances = {}

for target in TARGET_VARIABLES:
    X_test = test_data_per_label[target]
    X = data_per_label[target]
    print(f"  Training for {target}...")
    
    y = data_per_label[target][target].dropna()
    X_subset = X.loc[y.index]
    X_test = X_test.to_numpy()
    cols = X_subset.drop(columns=[target]).columns
    print(f"Size for data is {X_subset.shape} and features are {len(cols)}")
    
    X_train_fold, X_val_fold, y_train_fold, y_val_fold = train_test_split(
        X_subset.drop(columns=[target]), y, test_size=0.2, random_state=10
    )
    
    X_train_fold = X_train_fold.to_numpy()
    X_val_fold = X_val_fold.to_numpy()
    y_train_fold = y_train_fold.to_numpy()
    y_val_fold = y_val_fold.to_numpy()

    # XGBoost
    xgb_model = XGBRegressor(n_estimators= 1488, learning_rate= 0.010456188013762864, max_depth= 5, reg_lambda= 9.970345982204618, random_state = 4)
    xgb_model.fit(X_train_fold, y_train_fold, eval_set=[(X_val_fold, y_val_fold)], verbose=False)
    xgb_oof_preds = xgb_model.predict(X_val_fold)
    test_preds_xgb = xgb_model.predict(X_test)
    importance_values = xgb_model.feature_importances_
    importance_dict = dict(zip(cols, importance_values))
    all_importances[target] = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)

    # LightGBM
    # lgb_model = lgb.LGBMRegressor(**LGBM_PARAMS)
    # lgb_model.fit(X_train_fold, y_train_fold,
    #               eval_set=[(X_val_fold, y_val_fold)],
    #               callbacks=[lgb.early_stopping(stopping_rounds=30, verbose=False)])
    # oof_preds_lgb = lgb_model.predict(X_val_fold)
    # test_preds_lgb = lgb_model.predict(X_test)


    # Extra Trees
    et_model = ExtraTreesRegressor()
    et_model.fit(X_train_fold, y_train_fold)
    oof_preds_et = et_model.predict(X_val_fold)
    test_preds_et = et_model.predict(X_test)

    # # CatBoost
    # cat_model = CatBoostRegressor(verbose=0)
    # cat_model.fit(X_train_fold, y_train_fold, eval_set=(X_val_fold, y_val_fold))
    # oof_preds_cat = cat_model.predict(X_val_fold)
    # test_preds_cat = cat_model.predict(X_test)


    # # Lasso
    # lasso_model = Lasso()
    # lasso_model.fit(X_train_fold, y_train_fold)
    # oof_preds_lasso = lasso_model.predict(X_val_fold)
    # test_preds_lasso = lasso_model.predict(X_test)

    # # Random Forest
    rff_model = RandomForestRegressor(random_state = 42)
    rff_model.fit(X_train_fold, y_train_fold)
    oof_preds_rff = rff_model.predict(X_val_fold)
    test_preds_rff = rff_model.predict(X_test)

    rff_model = RandomForestRegressor(random_state = 69)
    rff_model.fit(X_train_fold, y_train_fold)
    oof_preds_rff1 = rff_model.predict(X_val_fold)
    test_preds_rff1 = rff_model.predict(X_test)

    rff_model = RandomForestRegressor(random_state = 21)
    rff_model.fit(X_train_fold, y_train_fold)
    oof_preds_rff2 = rff_model.predict(X_val_fold)
    test_preds_rff2 = rff_model.predict(X_test)
    
    # Averaging predictions
    val_preds_all = np.vstack([
        xgb_oof_preds,
        #oof_preds_lgb,
        oof_preds_et,
        #oof_preds_cat,
        #oof_preds_lasso,
        oof_preds_rff,
        oof_preds_rff1,
        oof_preds_rff2
    ])
    final_oof_preds = np.mean(val_preds_all, axis=0)

    test_preds_all = np.vstack([
        test_preds_xgb,
        #test_preds_lgb,
        test_preds_et,
        #test_preds_cat,
        #test_preds_lasso,
        test_preds_rff,
        test_preds_rff1,
        test_preds_rff2
    ])
    final_test_preds = np.mean(test_preds_all, axis=0)

    # Save predictions
    gbdt_predictions_df[target] = final_test_preds
    gbdt_oof_df[target] = final_oof_preds

    # MAE Computation
    mae = mean_absolute_error(y_val_fold, final_oof_preds)
    mae_scores[target] = mae
    print(f"    MAE on validation set for {target}: {mae:.4f}")


##########################Density Training
TARGET_VARIABLES = [ "Density"]
RANDOM_STATE = 42
gbdt_oof_df = {}
mae_scores = {}
all_importances = {}
et_params = {'n_estimators': 100,
 'criterion': 'squared_error',
 'max_depth': None,
 'min_samples_split': 2,
 'min_samples_leaf': 1,
 'min_weight_fraction_leaf': 0.0,
 'max_features': 1.0,
 'max_leaf_nodes': None,
 'min_impurity_decrease': 0.0,
 'bootstrap': False,
 'oob_score': False,
 'n_jobs': None,
 'random_state': None,
 'verbose': 0,
 'warm_start': False,
 'ccp_alpha': 0.0,
 'max_samples': None}

for target in TARGET_VARIABLES:
    X_test = test_data_per_label[target]
    X = data_per_label[target]
    print(f"  Training for {target}...")
    
    y = data_per_label[target][target].dropna()
    X_subset = X.loc[y.index]
    X_test = X_test.to_numpy()
    cols = X_subset.drop(columns=[target]).columns
    print(f"Size for data is {X_subset.shape} and features are {len(cols)}")
    
    X_train_fold, X_val_fold, y_train_fold, y_val_fold = train_test_split(
        X_subset.drop(columns=[target]), y, test_size=0.2, random_state=10
    )
    
    X_train_fold = X_train_fold.to_numpy()
    X_val_fold = X_val_fold.to_numpy()
    y_train_fold = y_train_fold.to_numpy()
    y_val_fold = y_val_fold.to_numpy()

    # XGBoost
    if target == "Tg":
        xgb_model = XGBRegressor(n_estimators= 2173, learning_rate= 0.0672418745539774, max_depth= 6, reg_lambda= 5.545520219149715, random_state = 4)
    elif target == "Rg":
        xgb_model = XGBRegressor(n_estimators= 520, learning_rate= 0.07324113948440986, max_depth= 5, reg_lambda=0.9717380315982088, random_state = 4)
    elif target == "FFV":
        xgb_model = XGBRegressor(n_estimators= 2202, learning_rate= 0.07220580588586338, max_depth= 4, reg_lambda= 2.8872976032666493, random_state = 4)
    elif target == "Tc":
        xgb_model = XGBRegressor(n_estimators= 1488, learning_rate= 0.010456188013762864, max_depth= 5, reg_lambda= 9.970345982204618, random_state = 4)
    elif target == "Density":
        xgb_model = XGBRegressor(n_estimators= 1958, learning_rate= 0.10955287548172478, max_depth= 5, reg_lambda= 3.074470087965767, random_state = 4)

    xgb_model.fit(X_train_fold, y_train_fold, eval_set=[(X_val_fold, y_val_fold)], verbose=False)
    xgb_oof_preds = xgb_model.predict(X_val_fold)
    test_preds_xgb = xgb_model.predict(X_test)
    importance_values = xgb_model.feature_importances_
    importance_dict = dict(zip(cols, importance_values))
    all_importances[target] = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)

    # # LightGBM
    # lgb_model = lgb.LGBMRegressor(**LGBM_PARAMS)
    # lgb_model.fit(X_train_fold, y_train_fold,
    #               eval_set=[(X_val_fold, y_val_fold)],
    #               callbacks=[lgb.early_stopping(stopping_rounds=30, verbose=False)])
    # oof_preds_lgb = lgb_model.predict(X_val_fold)
    # test_preds_lgb = lgb_model.predict(X_test)

    # Extra Trees
    et_model = ExtraTreesRegressor(**et_params)
    et_model.fit(X_train_fold, y_train_fold)
    oof_preds_et = et_model.predict(X_val_fold)
    test_preds_et = et_model.predict(X_test)

    # # CatBoost
    # cat_model = CatBoostRegressor(verbose=0)
    # cat_model.fit(X_train_fold, y_train_fold, eval_set=(X_val_fold, y_val_fold))
    # oof_preds_cat = cat_model.predict(X_val_fold)
    # test_preds_cat = cat_model.predict(X_test)


    # # Lasso
    # lasso_model = Lasso()
    # lasso_model.fit(X_train_fold, y_train_fold)
    # oof_preds_lasso = lasso_model.predict(X_val_fold)
    # test_preds_lasso = lasso_model.predict(X_test)

    # # Random Forest
    rff_model = RandomForestRegressor(random_state = 42)
    rff_model.fit(X_train_fold, y_train_fold)
    oof_preds_rff = rff_model.predict(X_val_fold)
    test_preds_rff = rff_model.predict(X_test)

    # rff_model = RandomForestRegressor(random_state = 69)
    # rff_model.fit(X_train_fold, y_train_fold)
    # oof_preds_rff1 = rff_model.predict(X_val_fold)
    # test_preds_rff1 = rff_model.predict(X_test)

    # rff_model = RandomForestRegressor(random_state = 21)
    # rff_model.fit(X_train_fold, y_train_fold)
    # oof_preds_rff2 = rff_model.predict(X_val_fold)
    # test_preds_rff2 = rff_model.predict(X_test)
    
    # Averaging predictions
    val_preds_all = np.vstack([
        xgb_oof_preds,
        # oof_preds_lgb,
        oof_preds_et,
        # oof_preds_cat,
        # oof_preds_lasso,
        oof_preds_rff,
        # oof_preds_rff1,
        # oof_preds_rff2
    ])
    final_oof_preds = np.mean(val_preds_all, axis=0)

    test_preds_all = np.vstack([
        test_preds_xgb,
        # test_preds_lgb,
        test_preds_et,
        # test_preds_cat,
        # test_preds_lasso,
        test_preds_rff,
        # test_preds_rff1,
        # test_preds_rff2
    ])
    final_test_preds = np.mean(test_preds_all, axis=0)

    # Save predictions
    gbdt_predictions_df[target] = final_test_preds
    gbdt_oof_df[target] = final_oof_preds

    # MAE Computation
    mae = mean_absolute_error(y_val_fold, final_oof_preds)
    mae_scores[target] = mae
    print(f"    MAE on validation set for {target}: {mae:.4f}")


#####################Rg Training
TARGET_VARIABLES = ["Rg"]
RANDOM_STATE = 42
gbdt_oof_df = {}
mae_scores = {}
all_importances = {}

lgb_params = {
    'n_estimators': 129489,
    'max_depth': 12,
    'num_leaves': 37,
    'min_child_samples': 53,
    'min_gain_to_split': 0.029755369991863734,
    'learning_rate': 0.0014160990179217505,
    'subsample': 0.6773541064056658,
    'colsample_bytree': 0.611740418793298,
    'reg_lambda': 1.0568435885108063,
    'reg_alpha': 4.250107666550625e-05
}

for target in TARGET_VARIABLES:
    X_test = test_data_per_label[target]
    X = data_per_label[target]
    print(f"  Training for {target}...")
    
    y = data_per_label[target][target].dropna()
    X_subset = X.loc[y.index]
    X_test = X_test.to_numpy()
    cols = X_subset.drop(columns=[target]).columns
    print(f"Size for data is {X_subset.shape} and features are {len(cols)}")
    
    X_train_fold, X_val_fold, y_train_fold, y_val_fold = train_test_split(
        X_subset.drop(columns=[target]), y, test_size=0.2, random_state=10
    )
    
    X_train_fold = X_train_fold.to_numpy()
    X_val_fold = X_val_fold.to_numpy()
    y_train_fold = y_train_fold.to_numpy()
    y_val_fold = y_val_fold.to_numpy()

    # XGBoost
    if target == "Tg":
        xgb_model = XGBRegressor(n_estimators= 2173, learning_rate= 0.0672418745539774, max_depth= 6, reg_lambda= 5.545520219149715, random_state = 4)
    elif target == "Rg":
        xgb_model = XGBRegressor(n_estimators= 520, learning_rate= 0.07324113948440986, max_depth= 5, reg_lambda=0.9717380315982088, random_state = 4)
    elif target == "FFV":
        xgb_model = XGBRegressor(n_estimators= 2202, learning_rate= 0.07220580588586338, max_depth= 4, reg_lambda= 2.8872976032666493, random_state = 4)
    elif target == "Tc":
        xgb_model = XGBRegressor(n_estimators= 1488, learning_rate= 0.010456188013762864, max_depth= 5, reg_lambda= 9.970345982204618, random_state = 4)
    elif target == "Density":
        xgb_model = XGBRegressor(n_estimators= 1958, learning_rate= 0.10955287548172478, max_depth= 5, reg_lambda= 3.074470087965767, random_state = 4)

    # xgb_model.fit(X_train_fold, y_train_fold, eval_set=[(X_val_fold, y_val_fold)], verbose=False)
    # xgb_oof_preds = xgb_model.predict(X_val_fold)
    # test_preds_xgb = xgb_model.predict(X_test)
    # importance_values = xgb_model.feature_importances_
    # importance_dict = dict(zip(cols, importance_values))
    # all_importances[target] = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)

    # LightGBM
    lgb_model = lgb.LGBMRegressor(**lgb_params)
    lgb_model.fit(X_train_fold, y_train_fold,
                  eval_set=[(X_val_fold, y_val_fold)],
                  callbacks=[lgb.early_stopping(stopping_rounds=30, verbose=False)])
    oof_preds_lgb = lgb_model.predict(X_val_fold)
    test_preds_lgb = lgb_model.predict(X_test)

    # # Extra Trees
    # et_model = ExtraTreesRegressor()
    # et_model.fit(X_train_fold, y_train_fold)
    # oof_preds_et = et_model.predict(X_val_fold)
    # test_preds_et = et_model.predict(X_test)

    # # CatBoost
    # cat_model = CatBoostRegressor(verbose=0)
    # cat_model.fit(X_train_fold, y_train_fold, eval_set=(X_val_fold, y_val_fold))
    # oof_preds_cat = cat_model.predict(X_val_fold)
    # test_preds_cat = cat_model.predict(X_test)


    # # Lasso
    # lasso_model = Lasso()
    # lasso_model.fit(X_train_fold, y_train_fold)
    # oof_preds_lasso = lasso_model.predict(X_val_fold)
    # test_preds_lasso = lasso_model.predict(X_test)

    # # # Random Forest
    # rff_model = RandomForestRegressor(random_state = 42)
    # rff_model.fit(X_train_fold, y_train_fold)
    # oof_preds_rff = rff_model.predict(X_val_fold)
    # test_preds_rff = rff_model.predict(X_test)

    # rff_model = RandomForestRegressor(random_state = 69)
    # rff_model.fit(X_train_fold, y_train_fold)
    # oof_preds_rff1 = rff_model.predict(X_val_fold)
    # test_preds_rff1 = rff_model.predict(X_test)

    # rff_model = RandomForestRegressor(random_state = 21)
    # rff_model.fit(X_train_fold, y_train_fold)
    # oof_preds_rff2 = rff_model.predict(X_val_fold)
    # test_preds_rff2 = rff_model.predict(X_test)
    
    # Averaging predictions
    val_preds_all = np.vstack([
        # xgb_oof_preds,
        oof_preds_lgb,
        # oof_preds_et,
        # oof_preds_cat,
        # oof_preds_lasso,
        # oof_preds_rff,
        # oof_preds_rff1,
        # oof_preds_rff2
    ])
    final_oof_preds = np.mean(val_preds_all, axis=0)

    test_preds_all = np.vstack([
        # test_preds_xgb,
        test_preds_lgb,
        # test_preds_et,
        # test_preds_cat,
        # test_preds_lasso,
        # test_preds_rff,
        # test_preds_rff1,
        # test_preds_rff2
    ])
    final_test_preds = np.mean(test_preds_all, axis=0)

    # Save predictions
    gbdt_predictions_df[target] = final_test_preds
    gbdt_oof_df[target] = final_oof_preds

    # MAE Computation
    mae = mean_absolute_error(y_val_fold, final_oof_preds)
    mae_scores[target] = mae
    print(f"    MAE on validation set for {target}: {mae:.4f}")

#########################
gbdt_predictions_df.to_csv('submission.csv', index=False)
gbdt_predictions_df.head()