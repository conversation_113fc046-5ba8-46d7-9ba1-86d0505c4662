##############model config

XGB_PARAMS = {
    'objective': 'reg:squarederror',
    'eval_metric': 'mae',
    'n_estimators': 200,
    'learning_rate': 0.03,
    'max_depth': 10,
    'subsample': 0.7,
    'colsample_bytree': 0.8,
    'min_child_weight': 5,
    'reg_alpha': 0.01,
    'reg_lambda': 0.01,
    'tree_method': 'hist',
    'device': 'cuda',  # or 'cpu' if no GPU
    'random_state': 42,
    'n_jobs': -1,
}


HGBR_PARAMS = {
    'loss': 'absolute_error',   # same as MAE
    'max_depth': 12,
    'learning_rate': 0.03,
    'max_iter': 600,
    'early_stopping': True,
    'validation_fraction': 0.1,
    'random_state': 42
}

LGBM_PARAMS = {
    'device_type': 'cpu',
    'n_estimators': 1_000,
    'objective': 'regression_l1',
    'metric': 'mae',
    'verbosity': -1,
    'num_leaves': 50,
    'min_data_in_leaf': 2,
    'learning_rate': 0.01,
    'max_bin': 500,
    'feature_fraction': 0.7,
    'bagging_fraction': 0.7,
    'bagging_freq': 1,
    'lambda_l1': 2,
    'lambda_l2': 2,
}

EXTRATREES_PARAMS = {
       'n_estimators': 100, 
        'max_depth': 8, 
        'min_samples_split': 3, 
        'min_samples_leaf': 1, 
        'max_features': 'auto', 
        'bootstrap': False,
        'random_state': 42,
        'n_jobs': -1
}
CATBOOST_PARAMS = {
        'iterations': 500,  # reduce for speed
        'learning_rate': 0.07,
        'depth': 5, 
        'l2_leaf_reg': 0.20, 
        'bagging_temperature': 0.76, 
        'random_strength': 0.010, 
        'border_count': 123,
        'loss_function': 'MAE',
        'eval_metric': 'MAE',
        'verbose': False,
        'task_type': 'CPU',  # ✅ use CPU
        'random_seed': 42,
}
LASSO_PARAMS = {
    'alpha': 0.001,      # regularization strength
    'max_iter': 1000,
    'random_state': 42
}
SVR_PARAMS = {
    'C': 1.0,
    'epsilon': 0.1,
    'kernel': 'rbf'
}