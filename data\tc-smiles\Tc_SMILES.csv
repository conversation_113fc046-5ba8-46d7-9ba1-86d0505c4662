TC_mean,SMILES
0.2445,*CC(*)C
0.2253333333333333,*CC(*)CC
0.24633333333333332,*CC(*)CCC
0.1868,*CC(*)C(C)C
0.23066666666666666,*CC(*)CCCC
0.20833333333333334,*CC(*)CC(C)C
0.25839999999999996,*CC(*)CCCCC
0.216,*CC(*)CCC(C)C
0.229,*CC(*)CCC(C)CC
0.26699999999999996,*CC(*)CCCCCCC
0.28600000000000003,*CC(*)CCCCCCCC
0.2125,*CC(*)(C)C
0.17,*CC(*)(C)CC
0.19566666666666666,*CCC(*)(C)C
0.20266666666666666,*CC(C)C(*)(C)C
0.236,*CCCC(*)(C)C
0.23399999999999999,*CCCC(*)(C)CC
0.371,*CCCCCCCC(*)C
0.21750000000000003,*CC(*)C1CC1
0.209,*CC(*)C1CCC1
0.20166666666666666,*CC(*)C1CCCC1
0.2283333333333333,*CC(*)CC1CCCC1
0.19066666666666668,*CC(*)C1CCCCC1
0.22560000000000002,*CC(*)CC1CCCCC1
0.19633333333333333,*CC(*)Cc1ccccc1
0.18033333333333332,*CC(*)CCc1ccccc1
0.15066666666666664,*CC(*)C(C)c1ccccc1
0.3353333333333333,*CC1CCC(*)C1
0.238,*CC(*)CCCCCC
0.33575,*CC(*)CCCCCCCCCC
0.339,*CC(*)CCCCCCCCCCCC
0.375,*CC(*)CCCCCCCCCCCCCCCC
0.25733333333333336,*CC(*)CCCC(C)C
0.18974999999999997,*CC(*)C(C)CC
0.18833333333333332,*CC(*)C(CC)CC
0.219,*CC(*)CC(CC)CC
0.202,*CC(*)CC(C)CC
0.1735,*CC(*)CC(C)(C)C
0.18633333333333332,*CC(*)CC(C)(C)CC
0.216,*CC(*)CCCC(C)(C)C
0.21766666666666667,*CC(*)CCC1CCCCC1
0.24633333333333332,*CC(*)CCCC1CCCCC1
0.164,*CC1(*)CCC(C)CC1
0.17400000000000002,*CC(*)(C)CCC
0.2535,*/C=C/CCC*
0.244,*/C=C/CC*
0.24866666666666667,*/C=C/CC*
0.272,*/C=C/CC*
0.20333333333333334,*C(*)C
0.23299999999999998,*CCC(C)C(*)C
0.28633333333333333,*CCC1CCCC1*
0.35716666666666663,*/C=C/CCCCCCCC*
0.40199999999999997,*CC(*)CCCCCCCCCCCCCCCCCCCC
0.34099999999999997,*CC(*)CCCCCCCCCCCCCC
0.33,*/C=C/CCCCCC*
0.13633333333333333,*CC(*)C(C)(C)C
0.3383333333333333,*CC(*)CCCCCCCCCCCCC
0.206,*C(*)CC
0.32066666666666666,*CC(*)CCCCCCCCCCC
0.3193333333333333,*CC(*)CCCCCCCCC
0.18066666666666667,*CC(*)C1CCCC(C)C1
0.19966666666666666,*CC(*)C1CCC(C)CC1
0.18075000000000002,*CC(*)C1CC=CCC1
0.269,*CC/C=C(/*)CCCCCCC
0.21,*CC/C=C(/*)C(C)C
0.2796,*/C=C/CCC*
0.19299999999999998,*CC(*)CCC(C)(C)C
0.163,*C1CCC1*
0.262,*/C=C/*
0.31433333333333335,*/C=C/CCCCC*
0.26033333333333336,*/C=C/C1CCC(*)C1
0.203,*/C=C/C(C*)C(C)CC
0.22333333333333336,*CC/C=C(/*)CCC
0.4142,*/C=C/CCCCCCCCCC*
0.526,*/C=C/*
0.14433333333333334,*CC1(*)CCCCC1
0.24866666666666667,*/C=C/C(C)CCCCC*
0.20825,*CC/C=C(/*)C(C)(C)C
0.20459999999999998,*CC(*)c1ccccc1
0.19033333333333333,*CC(*)c1ccccc1C
0.18433333333333332,*CC(*)c1ccccc1CC
0.20666666666666667,*CC(*)c1ccccc1COC
0.18466666666666667,*CC(*)c1ccccc1COCC
0.217,*CC(*)c1ccccc1COCCC
0.171,*CC(*)c1ccccc1COC(C)C
0.20966666666666667,*CC(*)c1ccccc1COCCCC
0.186,*CC(*)c1ccccc1COCCCCC
0.19433333333333333,*CC(*)c1ccccc1COCCC(C)C
0.19133333333333336,*CC(*)c1ccccc1COCc1ccccc1
0.18833333333333332,*CC(*)c1ccccc1COCCc1ccccc1
0.18366666666666664,*CC(*)c1ccccc1C(=O)NC
0.165,*CC(*)c1ccccc1C(=O)N(C)C
0.16366666666666665,*CC(*)c1ccccc1C(=O)Nc1ccccc1
0.17366666666666664,*CC(*)c1ccccc1C(=O)OC
0.18166666666666664,*CC(*)c1ccccc1C(=O)OCC
0.19299999999999998,*CC(*)c1ccccc1C(=O)OCCC
0.17366666666666664,*CC(*)c1ccccc1C(=O)OC(C)C
0.19533333333333336,*CC(*)c1ccccc1C(=O)OCCCC
0.16966666666666666,*CC(*)c1ccccc1C(=O)OCC(C)C
0.19233333333333333,*CC(*)c1ccccc1C(=O)OCCCCC
0.19299999999999998,*CC(*)c1ccccc1C(=O)OCCC(C)C
0.20566666666666666,*CC(*)c1ccccc1C(=O)OCCCCCC
0.16933333333333334,*CC(*)c1ccccc1C(=O)Oc1ccccc1
0.18533333333333335,*CC(*)c1ccccc1C(=O)OCCN(C)C
0.23149999999999998,*CC(*)c1ccc(C(=O)OCCN(C)C)cc1
0.19466666666666668,*CC(*)c1ccccc1OC
0.1436666666666667,*CC(*)c1ccccc1Cl
0.17466666666666666,*CC(*)c1ccccc1F
0.19733333333333336,*CC(*)c1cccc(C)c1
0.18600000000000003,*CC(*)c1cccc(CC)c1
0.19566666666666666,*CC(*)c1cccc(-c2ccc(-c3ccccc3)cc2)c1
0.14175000000000001,*CC(*)c1cccc(Cl)c1
0.16233333333333333,*CC(*)c1cccc(F)c1
0.18733333333333335,*CC(*)c1ccc(C)cc1
0.20133333333333334,*CC(*)c1ccc(CC)cc1
0.20966666666666667,*CC(*)c1ccc(CCCC)cc1
0.18400000000000002,*CC(*)c1ccc(C(C)(C)C)cc1
0.241,*CC(*)c1ccc(CCCCCC)cc1
0.26749999999999996,*CC(*)c1ccc(CCCCCCCC)cc1
0.277,*CC(*)c1ccc(CCCCCCCCC)cc1
0.2825,*CC(*)c1ccc(CCCCCCCCCC)cc1
0.3035,*CC(*)c1ccc(CCCCCCCCCCCC)cc1
0.33999999999999997,*CC(*)c1ccc(CCCCCCCCCCCCCC)cc1
0.34099999999999997,*CC(*)c1ccc(CCCCCCCCCCCCCCCC)cc1
0.3751666666666667,*CC(*)c1ccc(CCCCCCCCCCCCCCCCCC)cc1
0.23166666666666666,*CC(*)c1ccc(C(C)(C)O)cc1
0.22666666666666666,*CC(*)c1ccc(C(C)(O)CC)cc1
0.252,*CC(*)c1ccc(COCCCCCC)cc1
0.262,*CC(*)c1ccc(COCCOCCCC)cc1
0.246,*CC(*)c1ccc(C(=O)N(C)C)cc1
0.213,*CC(*)c1ccc(C(=O)N(CC)CC)cc1
0.225,*CC(*)c1ccc(C(=O)CCN2CCCCC2)cc1
0.24,*CC(*)c1ccc(C(=O)N2CCOCC2)cc1
0.20333333333333337,*CC(*)c1ccc(C(=O)OC)cc1
0.26633333333333337,*CC(*)c1ccc(C(=O)CCCCCCC)cc1
0.18566666666666665,*CC(*)c1ccc(C)cc1C
0.19799999999999998,*CC(*)c1ccc(C(C)C)cc1C(C)C
0.14133333333333334,*CC(*)c1ccc(F)cc1C
0.1366666666666667,*CC(*)c1ccc(Cl)cc1C
0.10066666666666667,*CC(*)c1ccc(Cl)cc1Cl
0.17300000000000001,*CC(*)c1cc(C)ccc1C
0.16366666666666665,*CC(*)c1cc(C(C)(C)C)ccc1C
0.16166666666666668,*CC(*)c1cc(C(C)C)ccc1C(C)C
0.11199999999999999,*CC(*)c1cc(Cl)ccc1Cl
0.14033333333333334,*CC(*)c1cc(F)ccc1F
0.171,*CC(*)c1cc(C)ccc1F
0.14933333333333335,*CC(*)c1cc(Br)ccc1OCCCC
0.124,*CC(*)c1cc(Br)ccc1OCC
0.132,*CC(*)c1cc(Br)ccc1OC
0.136,*CC(*)c1cc(Br)ccc1OCCC
0.124,*CC(*)c1cc(Br)ccc1OC(C)C
0.1545,*CC(*)c1cc(Br)ccc1OCCCCC
0.16749999999999998,*CC(*)c1cc(Br)ccc1OCCC(C)C
0.1905,*CC(*)c1ccc(C)c(C)c1
0.15400000000000003,*CC(*)c1ccc(Cl)c(C)c1
0.114,*CC(*)c1ccc(Cl)c(Cl)c1
0.1275,*CC(*)c1ccc(Cl)c(F)c1
0.16699999999999998,*CC(*)c1c(C)cc(C)cc1C
0.20950000000000002,*CC(*)c1cc(C)c(C)cc1C
0.164,*CC(*)(C)c1ccccc1
0.20133333333333334,*CC(*)(C)c1ccc(C(C)C)cc1
0.19400000000000003,*CC(*)c1cccc2ccccc12
0.22966666666666669,*CC(*)c1ccc(C(C)=O)cc1
0.1426666666666667,*CC(*)(C(=O)OC)c1ccccc1
0.2075,*CC(*)n1cc2ccccc2n1
0.151,*CC(c1ccccn1)C(c1ccccc1)C(*)c1ccccn1
0.20733333333333334,*CC(*)c1ccc(O)cc1
0.2005,*CC(*)c1ccc(OC(C)=O)cc1
0.2345,*CC(*)c1ccc(CCC)cc1
0.3,*CC(*)c1ccc(COCCCCCCOc2ccc(-c3ccc(OC)cc3)cc2)cc1
0.268,*CC(*)c1ccc(COCC(CC)CCCC)cc1
0.28200000000000003,*CC(*)c1ccc(COCCOCCCCCCCC)cc1
0.176,*CC(CC(*)(C#N)C(=O)OC)c1ccccc1
0.171,*CC(*)c1ccc(C(=O)O)c(C(=O)O)c1
0.199,*C([2H])([2H])C(*)([2H])c1c([2H])c([2H])c([2H])c([2H])c1[2H]
0.42400000000000004,*CC(*)O
0.2425,*CC(*)C=O
0.261,*CC(*)OC(=O)CCCCCCCCCCC
0.319,*CC(*)OC(=O)CCCCCCCCCCCCCCC
0.305,*CC(*)OC(=O)CCCCCCCCCCCCCCCCC
0.179,*CC(*)C(C)=O
0.203,*CC(*)C(=O)C(C)C
0.16699999999999998,*CC(*)(C)C(C)=O
0.20700000000000002,*CC(*)C#N
0.146,*CC(*)(C)C#N
0.196,*CC(*)c1ccccn1
0.147,*CC(*)n1c2ccccc2c2ccccc21
0.26149999999999995,*CC(*)OC
0.2465,*CC(*)OCC
0.21200000000000002,*CC(*)OC(=O)CC
0.16699999999999998,*CC(*)OC(=O)c1ccccc1
0.201,*CC(*)CCCN(CC(C)C)CC(C)C
0.1585,*CC(*)O[N+](=O)[O-]
0.189,*/C=C/C(C)C(*)C(=O)OC
0.21150000000000002,*CC(*)OC(C)C
0.20400000000000001,*CC(*)C(=O)C1CCCCC1
0.1915,*CC(*)C(=O)c1ccc(C(C)(C)C)cc1
0.1975,*CC(*)C(=O)c1ccc(C)cc1
0.223,*CC(*)OC(=O)c1ccc(OC(=O)CCC)cc1
0.086,*CC(*)OC(=O)C1(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C1(F)F
0.1995,*CC(*)c1cccs1
0.13,*C1Cc2ccccc2C1*
0.184,*CCC/C=C(/*)c1ccc(Cl)cc1
0.192,*CC(*)CNc1ccc([N+](=O)[O-])cn1
0.371,*CC(*)OC(=O)CCCCCCCCCCCCCCCCCCCCC
0.3868,*CC(*)OCCCCCCCCCCCCCCCCCC
0.2305,*CC(*)C(=O)OCC
0.2075,*CC(*)C(=O)OCCCC
0.2565,*CC(*)C(=O)OCCCCCC
0.3215,*CC(*)C(=O)OCCCCCCCCCCCCCC
0.3626666666666667,*CC(*)C(=O)OCCCCCCCCCCCCCCCC
0.3795,*CC(*)C(=O)OCCCCCCCCCCCCCCCCCC
0.35850000000000004,*CC(*)C(=O)OCCCCCCCCCCCCCCCCCCCCCC
0.11199999999999999,*CC(*)C(=O)OC(F)(C(F)(F)F)C(F)(F)F
0.15949999999999998,*CC(*)(C)C(=O)OC
0.16999999999999998,*CC(*)(C)C(=O)OCC
0.203,*CC(*)(C)C(=O)OCCCC
0.182,*CC(*)(C)C(=O)OC(C)CC
0.1525,*CC(*)(C)C(=O)OC(C)(C)C
0.184,*CC(*)(C)C(=O)OCCCCCC
0.2415,*CC(*)(C)C(=O)OCCCCCCCC
0.2665,*CC(*)(C)C(=O)OCCCCCCCCCCCC
0.3115,*CC(*)(C)C(=O)OCCCCCCCCCCCCCC
0.26,*CC(*)(C)C(=O)OCCCCCCCCCCCCCCCC
0.317,*CC(*)(C)C(=O)OCCCCCCCCCCCCCCCCCC
0.22399999999999998,*CC(*)(C)C(=O)OC1CCCCC1
0.153,*CC(*)(C)C(=O)OC1CCCCC1C
0.17099999999999999,*CC(*)(C)C(=O)OC1CCC(C)CC1
0.1545,*CC(*)(C)C(=O)OC1CCCC(C)C1
0.146,*CC(*)(C)C(=O)OC1CC(C)CC(C)(C)C1
0.184,*CC(*)(C)C(=O)Oc1ccccc1
0.1905,*CC(*)(C)C(=O)Oc1ccccc1C
0.1755,*CC(*)(C)C(=O)OCc1ccccc1
0.168,*CC(*)(C)C(=O)OCCc1ccccc1
0.16399999999999998,*CC(*)(C)C(=O)Oc1ccc2ccccc2c1
0.16799999999999998,*CC(*)(C)C(=O)OCCF
0.1325,*CC(*)(C)C(=O)OCCCl
0.128,*CC(*)(C)C(=O)OCCBr
0.11399999999999999,*CC(*)(C)C(=O)OC(C)C(F)(F)F
0.0755,*CC(*)(C)C(=O)OC(C(F)(F)F)C(F)(F)F
0.069,*CC(*)(C)C(=O)OC(F)(C(F)(F)F)C(F)(F)F
0.0465,*CC(*)(C)C(=O)OC(C(F)(F)F)(C(F)(F)F)C(F)(F)F
0.069,*CC(*)(C)C(=O)OC(F)(C(F)(F)F)C(F)(F)Cl
0.07350000000000001,*CC(*)(C)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
0.08299999999999999,*CC(*)(C)C(=O)Oc1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl
0.159,*CC(*)(C)C(=O)OCc1ccccc1Cl
0.21200000000000002,*CC(*)(C)C(=O)OCCOCC
0.172,*CC(*)(C)C(=O)OCC1CCCO1
0.172,*CC(*)(C)C(=O)OCc1ccco1
0.2005,*CC(*)(C)C(=O)OCCN(C)C
0.2375,*CC(*)(C)C(=O)OCCN(CC)CC
0.158,*CC(*)(C)C(=O)OCc1cccc([N+](=O)[O-])c1
0.1985,*CC(*)(Cl)C(=O)OCC
0.174,*CC(*)(Cl)C(=O)OC(C)CC
0.1365,*CC(*)(Cl)C(=O)OC1CCCCC1
0.203,*CC(*)C(=O)NC(C)C
0.21100000000000002,*CC(*)C(=O)N(CCCC)CCCC
0.3435,*CC(*)C(=O)NCCCCCCCCCCCC
0.3746666666666667,*CC(*)C(=O)NCCCCCCCCCCCCCC
0.3105,*CC(*)C(=O)NCCCCCCCCCCCCCCCC
0.383,*CC(*)C(=O)NCCCCCCCCCCCCCCCCCC
0.3665,*CC(*)C(=O)NCCCCCCCCCCCCCCCCCCCCCC
0.213,*CC(*)C(=O)OC(CC)CC
0.151,*CC(*)(C)C(=O)Oc1cccc(C)c1
0.23099999999999998,*CC(*)C(=O)OCC(CC)CCCC
0.281,*CC(*)C(=O)OCCCCCCCCC
0.201,*CC(*)C(=O)OC1CCCCC1
0.2205,*CC(*)C(=O)OC1CC(C)CC(C)(C)C1
0.259,*CC(*)(C)C(=O)OCCCCCCCCCC
0.14300000000000002,*CC(*)(C)C(=O)OC1CC2CCC1(C)C2(C)C
0.173,*CC(*)(Cl)C(=O)OCCC
0.178,*CC(*)(Cl)C(=O)OCCCC
0.161,*CC(*)(C#N)C(=O)OC
0.221,*CC(*)C(=O)OCC(C)CC
0.2405,*CC(*)C(=O)OC(C)CCCCC
0.245,*CC(*)C(=O)OC(C)CCCCCC
0.2095,*CC(*)C(=O)OCC(CC)CC
0.2115,*CC(*)C(=O)OCC(C)(C)C
0.20700000000000002,*CC(*)C(=O)OC(C)CC(C)C
0.23199999999999998,*CC(*)C(=O)OC(CCC(CC)CCCC)CC(C)C
0.195,*CC(*)C(=O)OCCC#N
0.181,*CC(*)C(=O)OCCSC
0.211,*CC(*)C(=O)OCCCSC
0.2175,*CC(*)C(=O)OCCSCC
0.2005,*CC(*)C(=O)OCCCCSC
0.21550000000000002,*CC(*)C(=O)OCCCSCC
0.183,*CC(*)C(=O)OCCSCC#N
0.1875,*CC(*)C(=O)OCCSCCC#N
0.18,*CC(*)C(=O)OCCSCCCC#N
0.219,*CC(*)C(=O)OCCCSCCC#N
0.2175,*CC(*)C(=O)OCCCCCCSCC#N
0.1635,*CC(*)C(=O)OC12CC3CC(C)(CC(C)(C3)C1)C2
0.218,*CC(*)C(=O)N1CCCCC1
0.212,*CC(*)C(=O)N1CCOCC1
0.196,*CC(*)C(=O)Oc1ccccc1
0.1735,*CC(*)C(=O)Oc1ccccc1C
0.20550000000000002,*CC(*)C(=O)Oc1cccc(C)c1
0.193,*CC(*)C(=O)Oc1ccc(C)cc1
0.175,*CC(*)C(=O)Oc1ccccc1C(C)(C)C
0.2245,*CC(*)C(=O)Oc1ccc(C(C)(C)C)cc1
0.20800000000000002,*CC(*)C(=O)Oc1ccc(OC)cc1
0.23299999999999998,*CC(*)C(=O)Oc1ccc(C(=O)OCCCC)cc1
0.20550000000000002,*CC(*)C(=O)Oc1ccc(-c2ccccc2)cc1
0.1985,*CC(*)C(=O)OCc1ccccc1
0.196,*CC(*)C(=O)OCCc1ccccc1
0.221,*CC(*)C(=O)Oc1cccc(N(C)C)c1
0.1775,*CC(*)C(=O)N(C)c1ccccc1
0.1845,*CC(*)C(=O)Oc1ccc(C#N)cc1
0.182,*CC(*)C(=O)OCc1ccc(C#N)cc1
0.166,*CC(*)C(=O)Oc1ccccc1Cl
0.119,*CC(*)C(=O)Oc1ccc(Cl)cc1Cl
0.08499999999999999,*CC(*)C(=O)Oc1c(Cl)c(Cl)c(Cl)c(Cl)c1Cl
0.16449999999999998,*CC(*)C(=O)Oc1cccc2ccccc12
0.21250000000000002,*CC(*)C(=O)N(C)C
0.252,*CC(*)C(=O)NCCCC
0.22599999999999998,*CC(*)C(=O)NC(C)CC
0.199,*CC(*)C(=O)NC(C)(C)C
0.1865,*CC(*)C(=O)N(C(C)C)C(C)C
0.1895,*CC(*)(C)C(=O)OCCCCC
0.1935,*CC(*)(C)C(=O)OCC(CC)CCCC
0.1895,*CC(*)(C)C(=O)OCCC(C)(C)C
0.16749999999999998,*CC(*)(C)C(=O)OC(C)C(C)(C)C
0.198,*CC(*)(C)C(=O)OCCC(C)CC(C)(C)C
0.1815,*CC(*)(C)C(=O)OCCOC
0.184,*CC(*)(C)C(=O)OCCNC(C)(C)C
0.138,*CC(*)(C)C(=O)OCCC#N
0.1895,*CC(*)(C)C(=O)OCC[N+](=O)[O-]
0.095,*CC(*)(C)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)F
0.084,*CC(*)(C)C(=O)OC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
0.09,*CC(*)(C)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)F
0.199,*CC(*)(C)C(=O)OCCS(=O)CC
0.1305,*CC(*)(C)C(=O)OCC1(C)COCOC1
0.1525,*CC(*)(C)C(=O)OCC1(C)COC(C)(C)OC1
0.16199999999999998,*CC(*)(C)C(=O)OCC(C)(C)C1OCC(C)(C)CO1
0.1245,*CC(*)(C)C(=O)OC12CC3CC(CC(C3)C1)C2
0.122,*CC(*)(C)C(=O)OC12CC3CC(C)(CC(C)(C3)C1)C2
0.1655,*CC(*)(C)C(=O)Oc1ccc(CC#N)cc1
0.14500000000000002,*CC(*)(CC)C(=O)OCC
0.16899999999999998,*CC(*)(CC(=O)OCC)C(=O)OCC
0.22499999999999998,*CC(*)(CC(=O)OCCCC)C(=O)OCCCC
0.179,*CC(*)(C#N)C(=O)OCCCC
0.1365,*CC(*)(CF)C(=O)OC
0.16849999999999998,*CC(*)(CF)C(=O)OCC
0.14650000000000002,*CC(*)(Cl)C(=O)OC(C)C
0.16949999999999998,*CC(*)(CC(=O)OC1CCCCC1)C(=O)OC1CCCCC1
0.19,*CC(*)(CC(=O)OCC1CCCCC1)C(=O)OCC1CCCCC1
0.213,*CC(*)(CC(=O)OCCC1CCCCC1)C(=O)OCCC1CCCCC1
0.199,*CC(*)(CC(=O)OCCCC1CCCCC1)C(=O)OCCCC1CCCCC1
0.154,*CC(*)(CC(=O)Oc1ccccc1)C(=O)Oc1ccccc1
0.176,*CC(*)(CC(=O)OCc1ccccc1)C(=O)OCc1ccccc1
0.199,*CC(*)(CC(=O)OCCc1ccccc1)C(=O)OCCc1ccccc1
0.20550000000000002,*CC(*)(CC(=O)OCCCc1ccccc1)C(=O)OCCCc1ccccc1
0.158,*CC(*)C(=O)Oc1ccc(Cl)cc1
0.1345,*CC(*)(C)C(=O)Oc1ccc(C#N)cc1
0.224,*CC(*)C(=O)OCCC(C)C
0.217,*CC(*)C(=O)OCC(C)CCC
0.086,*CC(*)C(=O)OCC(F)(F)C(F)(F)OC(F)(F)C(F)(F)C(F)(F)C(F)(F)F
0.212,*CC(*)C(=O)OCCC(C)OC
0.15300000000000002,*CC(*)(C)C(=O)OC1CCC(C(C)(C)C)CC1
0.113,*CC(*)(C)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)F
0.1555,*CC(*)(C)C(=O)OCC(C)(C)C
0.181,*CC(*)(C)C(=O)Oc1ccc(C(C)(C)C)cc1
0.152,*CC(*)(CC(=O)OC)C(=O)OC
0.14500000000000002,*CC(*)(C)C(N)=O
0.1545,*CC(*)(C)C(=O)Oc1ccc(C)cc1
0.14850000000000002,*C1C(=O)N(C2CCCCC2)C(=O)C1*
0.215,*CC(*)(CC(=O)OCCCCC)C(=O)OCCCCC
0.23199999999999998,*CC(*)(CC(=O)OCCCCCC)C(=O)OCCCCCC
0.1845,*CC(*)(CC(=O)OCCC)C(=O)OCCC
0.1635,*CC(*)(CC(=O)Oc1ccccc1C)C(=O)Oc1ccccc1C
0.165,*CC(*)(CC(=O)Oc1cccc(C)c1)C(=O)Oc1cccc(C)c1
0.182,*CC(*)(CC(=O)Oc1ccc(C)cc1)C(=O)Oc1ccc(C)cc1
0.1805,*CC(*)(C#N)C(=O)OCC
0.20750000000000002,*CCC(C(=O)OCC)C(*)C(=O)OCC
0.14300000000000002,*C1C(=O)N(c2ccccc2)C(=O)C1*
0.1405,*C1C(=O)N(c2ccc(C)cc2)C(=O)C1*
0.157,*CC(*)(C)C(=O)Oc1ccc(C)cc1C
0.1095,*CC(*)(C)C(=O)OCCN(CC)S(=O)(=O)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
0.217,*CC(*)(C#N)C(=O)OCCCCCC
0.1855,*CC(*)(C)C(=O)OCC1CO1
0.21750000000000003,*CC(*)(C)C(=O)OCCCCCCOc1ccc(-c2ccc(OC)cc2)cc1
0.19,*CC(*)(C)C(=O)OCCN(CC)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
0.2135,*CC(*)C(=O)OCCN(CC)c1ccc(/N=N/c2ccc([N+](=O)[O-])cc2)cc1
0.22999999999999998,*CC(*)C(=O)OCCCCCC(=O)Oc1ccc(-c2ccc(C#N)cc2)cc1
0.1985,*CCCC(*)(C)C(=O)O
0.109,*CC(*)(C)C(=O)NC(=O)Oc1c(Br)cc(S(=O)(=O)c2cc(Br)c(O)c(Br)c2)cc1Br
0.15,*CC(*)(C)C(=O)NC(=O)OC(C)COc1c(Br)cc(S(=O)(=O)c2cc(Br)c(OCC(C)O)c(Br)c2)cc1Br
0.2245,*CC(*)C(=O)OCCCCCCOc1ccc(C(=O)Oc2ccc(C#N)cc2)cc1
0.16949999999999998,*CC(*)(C)C(=O)OCCCCn1c2ccccc2c2ccccc21
0.2495,*CC(*)C(=O)NCCCCCC(=O)O
0.212,*CC(*)C(=O)OC(C)CCC(CC)CCCC
0.14250000000000002,*C1C(=O)OC(=O)C1*
0.3065,*CC(*)C(=O)NCCCCCCCC/C=C/CCCCCCCC
0.139,*CC(*)Cl
0.139,*C(F)(F)C(*)(F)Cl
0.316,*CCCCCCCCCCC(Cl)C(*)Cl
0.102,*/C(F)=C(\F)C(F)(C(*)(F)F)C(F)(F)F
0.148,*CC(*)CC(C)C(F)(F)F
0.1795,*CCC(Cl)C(*)Cl
0.235,*CC/C=C(/*)C
0.258,*CC/C=C(/*)C
0.218,*/C=C/C(C*)CCC
0.256,*CC/C=C(/*)C
0.27849999999999997,*C1C=CC(*)C1
0.246,*CO*
0.066,*OC(*)C(Cl)(Cl)Cl
0.17049999999999998,*OC(*)CCC#N
0.1995,*CC(C)(C)O*
0.181,*CC(O*)c1ccccc1
0.296,*CCCO*
0.353,*CCCCCCCCCCO*
0.43074999999999997,*CCCCCCCCCCCCO*
0.278,*CCCCCCOCO*
0.277,*CCCCCCCCCOCO*
0.297,*CCCCCCCCCCOCO*
0.406,*CCCCCCCCCCCCCCCCCCOCO*
0.36175,*CCCCCCCCCCOCCCCCCOCCCCCCO*
0.329,*CCCCCCCCCCOCCCCCCCCCCOCCCCO*
0.38525,*CCCCCCCCCCOCCCCCCCCCCOCCCCCO*
0.269,*CCCCCCCCCCOCCCCCCCCCCOCCCCCCO*
0.199,*CCCCSSCCCCO*
0.196,*CCCCSSCCCCOCO*
0.229,*CCOCCOC(=O)CCCCCCCCC(=O)O*
0.3215,*CCCCCCC(=O)NCCCCCCNC(=O)CCCCCCO*
0.3575,*CCCCCCCCC(=O)NCCCCCCNC(=O)CCCCCCCCO*
0.28900000000000003,*CCCCCCCCC(=O)NCCCCCOCCCCCNC(=O)CCCO*
0.303,*CCCCCCCCCCC(=O)NCCCCCOCCCCCNC(=O)CCCO*
0.287,*CCCCCCCCC(=O)NCCCCCOCCCCCNC(=O)CCCCO*
0.33499999999999996,*CCCCCCCCCCC(=O)NCCCCCOCCCCCNC(=O)CCCCO*
0.28500000000000003,*CCCCCNC(=O)CCCCCCOCCCCCCC(=O)NCCCCCO*
0.267,*CCCCCNC(=O)CCCCCCCCOCCCCCCCCC(=O)NCCCCCO*
0.259,*CCCC(=O)NCc1ccc(CNC(=O)CCCO*)cc1
0.217,*C(=O)NCCCCCCCCCCNC(=O)c1ccc(*)o1
0.216,*CCC(=O)OC(=O)CCc1ccc(*)o1
0.268,*Oc1ccc(S(=O)(=O)c2ccc(*)cc2)cc1
0.204,*Oc1ccc(S(=O)(=O)c2ccc(Oc3ccc(C(C)(C)c4ccc(*)cc4)cc3)cc2)cc1
0.224,*CCOCCOC(=O)C(CCCCCCCCC)C(=O)O*
0.34850000000000003,*CCOCCOC(=O)CCCCCCCCCCCCCCCCC(=O)O*
0.198,*CC(O)COc1ccc(C(C)(C)c2ccc(O*)cc2)cc1
0.1935,*Oc1c(C)cc(*)cc1-c1ccccc1
0.1485,*CC(O)COc1c(Cl)cc(C(C)(C)c2cc(Cl)c(O*)c(Cl)c2)cc1Cl
0.16949999999999998,*OC1CCCCC1*
0.324,*CCCCCCCCc1nnc(*)o1
0.32499999999999996,*CCCCCCCCCCCCCCCCCCCCOC(=O)COCC(=O)O*
0.267,*CCOCCOCCOC(=O)CCCCCCCC(=O)O*
0.11599999999999999,*CC(CCl)(CCl)O*
0.319,*Oc1cccc(NC(=O)CCCCCCCCCCC(=O)Nc2ccc(*)cc2)c1
0.3745,*Oc1ccc(NC(=O)CCCCCCCCCC(=O)Nc2ccc(*)cc2)cc1
0.27,*c1ccc(-c2nc3ccc(Oc4ccc5nc(*)c(-c6ccccc6)c(-c6ccccc6)c5c4)cc3c(-c3ccccc3)c2-c2ccccc2)cc1
0.0905,*ON(C(F)(F)F)C(F)(F)C(*)(F)F
0.2205,*Oc1ccc(Oc2ccc(C(=O)c3ccccc3-c3ccccc3C(=O)c3ccc(*)cc3)cc2)cc1
0.2685,*Oc1ccc(C(C)(C)c2ccc(Oc3ccc(C(=O)Nc4ccc(-c5ccc(NC(=O)c6ccc(*)cc6)cc5C(F)(F)F)c(C(F)(F)F)c4)cc3)cc2)cc1
0.344,*CCCCCCCCCCOc1ccc(OC(=O)c2ccc(OCCCCCCOc3ccc(C(=O)Oc4ccc(O*)cc4)cc3)cc2)cc1
0.329,*CCCCCCOC(=O)OCCCCCCOC(=O)OCCCCCCOc1ccc(-c2ccc(O*)cc2)cc1
0.27849999999999997,*CCc1ccc(*)o1
0.3795,*c1cccc(-c2nc3ccc(-c4ccc5oc(*)nc5c4)cc3o2)c1
0.172,*CS*
0.114,*SC(*)(F)F
0.223,*CCS*
0.175,*CCCCSS*
0.226,*CCCCSCCS*
0.234,*CCCCCCSCCS*
0.192,*CCCCCCSSCCCCSS*
0.241,*CCCCCCSCCCCCS*
0.3305,*CCCCCCCCCCSCCCCCCS*
0.3145,*CCCCCCCCCCCSCCCCCCS*
0.285,*CCCCCCCCCCC(=O)NCc1ccc(CNC(=O)CCCCCCCCCCS*)cc1
0.29600000000000004,*CCCCCCCCCCC(=O)NCCc1ccc(CCNC(=O)CCCCCCCCCCS*)cc1
0.2295,*CCCCCCCCCCSSCCCCCCSS*
0.24,*CC1CCC(COC(=O)NCCSCCCCCCSCCNC(=O)O*)CC1
0.27,*CCCCCCCCCCOC(=O)CCCCSCCCCC(=O)O*
0.322,*CCCCCCCCCCCCCCCCCCCCOC(=O)CCCCSCCCCC(=O)O*
0.224,*CCCCCCOC(=O)CCCCSCCCCC(=O)O*
0.192,*CCCCCCCCSSCCCCSS*
0.202,*CCCCCOC(=O)CCCCSCCCCC(=O)O*
0.161,*CCCCSSCCCSS*
0.223,*CCCC1CCN(C(=O)SSCCCCSSC(=O)N2CCC(*)CC2)CC1
0.212,*CCOC(=O)CCCCSCCCCC(=O)O*
0.214,*OC(C)COC(=O)CCCCSCCCCC(*)=O
0.145,*Sc1c(C)cc(*)cc1C
0.288,*CCc1ccc(*)s1
0.506,*c1ccc2c(c1)SC1=Nc3cc(-c4ccc5c(c4)N=C4Sc6cc(*)ccc6N=C4N5)ccc3NC1=N2
0.285,*CCCCCCCCCCOC(=O)CCC(=O)O*
0.263,*CCCCCCCCCCOC(=O)CCCC(=O)O*
0.34450000000000003,*CCCCCCCCCCOC(=O)CCCCCCC(=O)O*
0.29400000000000004,*CCCCCCCCCOC(=O)CCCCCCCC(=O)O*
0.3325,*CCCCCCCCCCOC(=O)CCCCCCCC(=O)O*
0.281,*CCCCCCCCCCOC(=O)CCCCCCCCC(=O)O*
0.3595,*CCCCCCCCCCCCCCC(=O)O*
0.3954,*CCCCCCCCCCCCCCCC(=O)O*
0.3415,*CCCCCCCCCCOC(=O)CCCCCCCCCCCCCCCCC(=O)O*
0.292,*CCCCCCCCCCCCOC(=O)CCCCC(=O)O*
0.284,*CCCCCCCCCCCCOC(=O)CCCCCCC(=O)O*
0.35550000000000004,*CCCCCCCCCCCCCCOC(=O)CCCCC(=O)O*
0.3165,*CCCCCCCCCCCCCCOC(=O)CCCCCCC(=O)O*
0.308,*CCCCCCCCCCCCCCCCOC(=O)CCCCCCC(=O)O*
0.354,*CC(CCCCCCCCCCCCCCCC)C(CCCCCCCCCCCCCCCC)COC(=O)c1ccc(C(=O)O*)cc1
0.364,*CCCOC(=O)CCCCCCCCCCCCCCCCC(=O)O*
0.279,*CC1CCC(COC(=O)CCCCCCCCCCC(=O)O*)CC1
0.2955,*CCCCCCCCCCOC(=O)CCCCCNC(=O)CCCCC(=O)NCCCCCC(=O)O*
0.307,*CCCCCCCCCCOC(=O)c1ccc(C(=O)NCCCCCNC(=O)c2ccc(C(=O)O*)cc2)cc1
0.278,*CCCCCCCCCCOC(=O)CCCCS(=O)(=O)CCCCC(=O)O*
0.306,*CCCCCCCCCCCCOC(=O)CCCCCNC(=O)CCCCC(=O)NCCCCCC(=O)O*
0.35450000000000004,*CCCCCCCCCCCCCCCCCCCCOC(=O)CCCCC(=O)O*
0.362,*CCCCCCCCCCCCCCCCCCCCOC(=O)CCC(C)CC(=O)O*
0.507,*CCCCCCCCCCCCCCCCCCCCOC(=O)CCCCCCCC(=O)O*
0.302,*CCCCCCCCCCCCCCCCCCCCOC(=O)CCCC(=O)O*
0.318,*CCCCCCCCCCCCCCCCCCCCOC(=O)CC(=O)O*
0.35450000000000004,*CCCCCCCCCCCCCCCCCCCCOC(=O)C(=O)O*
0.406,*CCCCCCCCCCCCCCCCCCCCOC(=O)CCCCCC(=O)O*
0.276,*CCCCCCCCCCCCCCCCCCCCOC(=O)CCCCCCCCC(=O)O*
0.33799999999999997,*CCCCCCCCCCCCCCCCCCCCOC(=O)CCCCCCC(=O)O*
0.362,*CCCCCCCCCCCCCCCCCCCCOC(=O)CCC(=O)O*
0.322,*CCCCCCCCCCCCCCCCCCCCOC(=O)CCCCS(=O)(=O)CCCCC(=O)O*
0.259,*CCCCCCCCCOC(=O)c1ccc(C(=O)NCCCCCCNC(=O)c2ccc(C(=O)O*)cc2)cc1
0.315,*CCCCCCCCCOC(=O)c1ccc(C(=O)NCCCCCNC(=O)c2ccc(C(=O)O*)cc2)cc1
0.304,*CCCCCCCCCCCCCCCCCCOC(=O)c1ccc(C(=O)O*)cc1
0.293,*CCCCCCCCOC(=O)CCCCCNC(=O)CCCCC(=O)NCCCCCC(=O)O*
0.261,*CCCCCCCCOC(=O)c1ccc(C(=O)NCCCCCCNC(=O)c2ccc(C(=O)O*)cc2)cc1
0.276,*CCCCCCCCOC(=O)CCCCCNC(=O)c1ccc(C(=O)NCCCCCC(=O)O*)cc1
0.319,*CCc1ccc(CCOC(=O)CCc2ccc(CCC(=O)O*)cc2)cc1
0.283,*Oc1ccc(/C=N/c2ccc(/N=C/c3ccc(OC(=O)CCCCC(*)=O)c(OC)c3)cc2)cc1OC
0.37,*Oc1ccc(/C=N/c2ccc(/N=C/c3ccc(OC(=O)c4cccc(C(*)=O)c4)c(OC)c3)cc2)cc1OC
0.274,*Oc1ccc(/C=N/c2ccc(/N=C/c3ccc(OC(=O)CCCCCCCCC(*)=O)c(OC)c3)cc2)cc1OC
0.23,*CCc1ccc(CCOC(=O)c2ccc(C(=O)O*)cc2)cc1
0.276,*CCCCCc1ccc(CCCCCOC(=O)c2ccc(C(=O)O*)cc2)cc1
0.241,*CCCCc1ccc(CCCCOC(=O)c2ccc(C(=O)O*)cc2)cc1
0.249,*CCCc1ccc(CCCOC(=O)c2ccc(C(=O)O*)cc2)cc1
0.224,*CCC(=O)Oc1ccc(OC(=O)CCN2CCN(*)CC2)cc1
0.258,*CCCCCCCCCCCCCCOC(=O)c1ccc(C(=O)NCCCCCNC(=O)c2ccc(C(=O)O*)cc2)cc1
0.333,*CCCCCCCCCCCCCCOC(=O)c1ccc(C(=O)NCCCCCCNC(=O)c2ccc(C(=O)O*)cc2)cc1
0.274,*CCCCCCCCCCCCCCOC(=O)c1ccc(C(=O)NCCNC(=O)c2ccc(C(=O)O*)cc2)cc1
0.242,*CCCCOC(=O)CCCCCNC(=O)CCCCC(=O)NCCCCCC(=O)O*
0.241,*CCCCOC(=O)CCCCCCCC(=O)O*
0.19,*CCCCOC(=O)c1ccccc1-c1ccccc1C(=O)O*
0.203,*CCCCOC(=O)c1cccc(-c2cccc(C(=O)O*)c2)c1
0.248,*CCCCOC(=O)c1ccc(-c2ccc(C(=O)O*)cc2)cc1
0.222,*CCCOC(=O)C1CCC(C(=O)O*)CC1
0.253,*CCC(C)CCC(=O)O*
0.234,*Oc1cc(CCC)cc(OC(=O)c2cccc(C(*)=O)c2)c1
0.222,*Oc1cccc(OC(=O)c2ccc(C(C)(C)c3ccc(C(*)=O)cc3)cc2)c1
0.244,*Oc1ccc(C(C)(C)c2ccc(OC(=O)CN(CC(*)=O)c3ccc(/N=N/c4ccc([N+](=O)[O-])cc4)cc3)cc2)cc1
0.315,*Oc1ccc(-c2ccc(-c3cc(-c4ccccc4)c(-c4ccc(-c5ccc(OC(=O)c6ccc(C(*)=O)cc6-c6ccccc6)cc5)cc4)c(-c4ccccc4)c3-c3ccccc3)cc2)cc1
0.257,*Oc1ccc(OC(=O)CCCCCCCCC(*)=O)cc1
0.227,*Cc1ccc2nc(-c3cc(-c4nc5ccc(*)cc5c(=O)o4)cc(N4C(=O)c5ccccc5C4=O)c3)oc(=O)c2c1
0.302,*CCCCNC(=O)CCCCC(=O)N*
0.327,*CCCCCNC(=O)CCCCC(=O)N*
0.301,*CCCCCC(=O)N*
0.278,*CCCCCCNC(=O)CCCCCC(=O)N*
0.345,*CCCCCCNC(=O)CCCCCCC(=O)N*
0.323,*CCCCCCCNC(=O)CCCCC(=O)N*
0.334,*CCCCCCCNC(=O)CCCCCC(=O)N*
0.329,*CCCCCCCC(=O)N*
0.291,*CCCCCCCCNC(=O)CCCCC(=O)N*
0.392,*CCCCCCCCNC(=O)CCCCCCCCC(=O)N*
0.3463333333333334,*CCCCCCCCNC(=O)CCCCCCCCCCC(=O)N*
0.42500000000000004,*CCCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCCCC(=O)N*
0.34099999999999997,*CCCCCCCCCCCC(=O)N*
0.33,*CCCCCCCCCCCCC(=O)N*
0.43414285714285716,*CCCCCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
0.4533333333333333,*CCCCCCCCCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
0.40800000000000003,*CCCCCCCCCCNC(=O)CCCCCCCCC(=O)N*
0.368,*CCc1ccc(CCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*)cc1
0.32999999999999996,*Cc1ccc(CNC(=O)CCCCCCCCCCCCCCCCC(=O)N*)cc1
0.3755,*CCCCCCCCCCCCCCCCCCNC(=O)CCc1ccc(CCC(=O)N*)cc1
0.361,*CCCCCCCCCNC(=O)CCCCCCCC(=O)N*
0.277,*CC(=O)N*
0.258,*CCCCNC(=O)CC/C=C/CCC(=O)N*
0.301,*CCCCCNC(=O)CCCCCCCC(=O)N*
0.278,*CCCCCCNC(=O)CC/C=C/CCC(=O)N*
0.309,*CCCCCCCCCCCCNC(=O)C(=O)N*
0.3125,*CCCCCCCCCCNC(=O)CC/C=C/CCC(=O)N*
0.254,*CCCCC(C)CC(=O)N*
0.29600000000000004,*CCCCCCCCCCC(=O)NCCCCCC(=O)N*
0.379,*CCCCCCCCCCCCCCCCC(=O)N*
0.30200000000000005,*CCNC(=O)CCCCCCCCC(=O)N*
0.2915,*CCCCNC(=O)CCCCCCC(=O)N*
0.355,*CCCCNC(=O)CCCCCCCCC(=O)N*
0.32299999999999995,*CCCCNC(=O)CCCCCCCCCC(=O)N*
0.332,*CCCCNC(=O)CCCCCCCCCCC(=O)N*
0.3415,*CCCCCNC(=O)CCCCCCCCCCCCC(=O)N*
0.355,*CCCCCCNC(=O)CCCCCCCCCCCCC(=O)N*
0.3955,*CCCCCCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
0.3235,*CCCCCCCNC(=O)CCCCCCCCC(=O)N*
0.4065,*CCCCCCCNC(=O)CCCCCCCCCC(=O)N*
0.308,*CCCCCCCCNC(=O)CCCCCCCC(=O)N*
0.381,*CCCCCCCCNC(=O)CCCCCCCCCCCCC(=O)N*
0.3725,*CCCCCCCCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
0.34850000000000003,*CCCCCCCCCNC(=O)CCCCCCCCC(=O)N*
0.312,*CCCCCCCCCNC(=O)CCCCCCCCCC(=O)N*
0.348,*CCCCCCCCCNC(=O)CCCCCCCCCCCC(=O)N*
0.422,*CCCCCCCCCCNC(=O)CCCCCCC(=O)N*
0.3685,*CCCCCCCCCCNC(=O)CCCCCCCCCCCC(=O)N*
0.342,*CCCCCCCCCCNC(=O)CCCCCCCCCCCCC(=O)N*
0.381,*CCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
0.3873333333333333,*CCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCC(=O)N*
0.38825,*CCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCCCC(=O)N*
0.3295,*CCCCCCCCCCCNC(=O)CCCCC(=O)N*
0.314,*CCCCCCCCCCCNC(=O)CCCCCCCCC(=O)N*
0.3745,*CCCCCCCCCCCCNC(=O)CCCCC(=O)N*
0.374,*CCCCCCCCCCCCNC(=O)CCCCCCCCC(=O)N*
0.3944285714285714,*CCCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCCCC(=O)N*
0.39449999999999996,*CCCCCCCCCCCCCCNC(=O)CCCCCCCCC(=O)N*
0.346,*CCCCCCCCCCCCCCNC(=O)CCCCCCCCCCC(=O)N*
0.39157142857142857,*CCCCCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCCCC(=O)N*
0.369,*CCCCCCCCCCCCCCCCCCNC(=O)CCCCCCCCC(=O)N*
0.43374999999999997,*CCCCCCCCCCCCCCCCCCNC(=O)CCCCCCCCCCC(=O)N*
0.3195,*CCc1ccc(CCNC(=O)CCCCCCCCCCCCCCCCCCCCC(=O)N*)cc1
0.35250000000000004,*CCCCCCN(C)C(=O)CCCCCCCCCCCCCCCCC(=O)N(*)C
0.3565,*CCCCCCCCCCC(=O)NCCCCCCNC(=O)CCCCO*
0.3245,*CCCCCCCCCCC(=O)NCCCCCCNC(=O)CCCO*
0.3185,*CCCCCCCCC(=O)NCCCCCCNC(=O)CCCCO*
0.286,*CCCCCCCCC(=O)NCCCCCCNC(=O)CCCO*
0.43250000000000005,*CCCCCCCCCCCCCCCCCCCCCC(=O)N*
0.35,*CCCCCCCCCCNC(=O)C(CCCCCCCCCCCC)C(=O)N*
0.4125,*CCCCCCCCCCNC(=O)CCCCCCCCC(=O)NCCCCCCCCCCNC(=O)C(=O)N*
0.44,*CCCCCCNC(=O)CCCCCCCCCCCCCCCCCCCCC(=O)N*
0.35050000000000003,*CCCCCCNC(=O)C(CCCCCCCCCCCCCCCCC)C(=O)N*
0.3235,*CCCCCCNC(=O)C(CCCCCCCCCCCCCCCC)C(=O)N*
0.354,*CCCCCCNC(=O)C(CCCCCCCCCCCCCCCCCC)C(=O)N*
0.33699999999999997,*CCCCCCNC(=O)C(CCCCCCCCCCCCCCC)C(=O)N*
0.319,*CCCCCCNC(=O)C(CCCCCCCCCCCCCC)C(=O)N*
0.2925,*CCCCCCNC(=O)C(CCCCCCCCCCCCC)C(=O)N*
0.347,*CCCCCCCCCNC(=O)C(CCCCCCCCCCCC)C(=O)N*
0.312,*CCCCCCCCCNC(=O)CCCCCCCCC(=O)NCCCCCCCCCNC(=O)C(=O)N*
0.331,*CCCCCCCCNC(=O)C(CCCCCCCCCCCC)C(=O)N*
0.4165,*CCCCCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
0.35450000000000004,*CCCCCNC(=O)CCCCCCCCCCCC(=O)N*
0.3515,*CCc1ccc(CCNC(=O)CCCCCCCCCCCCCCCC(=O)N*)cc1
0.3195,*CCCCCCCCCCCNC(=O)CCCCCCCCC(=O)NCCCCCCCCCCCNC(=O)C(=O)N*
0.359,*Cc1ccc(CNC(=O)CCCCCCCCCCCCCCCCCCCCC(=O)N*)cc1
0.34550000000000003,*CCCCCCCNC(=O)C(CCCCCCCCCCCC)C(=O)N*
0.268,*CCCCCNC(=O)O*
0.316,*CCCCCCCCCCOC(=O)NCCCCCCCCCCNC(=O)O*
0.348,*CCCCCCCCCCCCCCCCOC(=O)NCCCCCCNC(=O)O*
0.275,*CCCCCCCCCCCCCCCCOC(=O)NCCCCCCCCCCNC(=O)O*
0.3835,*CCCCCCCCCCCCCNC(=O)CCCCCCCCCCCC(=O)N*
0.328,*CCCCCCNC(=O)N*
0.3545,*CCCCCCCCCCCCNC(=O)NCCCCCCNC(=O)N*
0.4,*CCCCCCCCCCCCCCNC(=O)NCCCCCCNC(=O)N*
0.383,*CCCCCCCCCCCCCCCCCCNC(=O)NCCCCCCNC(=O)N*
0.344,*CCCCCCCCCCCCCCNC(=O)NCCCCCCCCCCNC(=O)N*
0.39,*CCCCCCCCCCCCCCCCCCNC(=O)NCCCCCCCCCCNC(=O)N*
0.366,*CCCCCCCCCCNC(=O)NCCCCCCCCNC(=O)N*
0.269,*c1ccc(NC(=O)Nc2ccc(NC(=O)Nc3ccc(-c4nc(-c5ccc([N+](=O)[O-])cc5)[nH]c4*)cc3)cc2)cc1
0.23,*CCCC1CC(=O)N(*)C(=O)C1
0.252,*c1ccc(Sc2ccc(N3C(=O)c4ccc(-c5ccc6c(c5)C(=O)N(*)C6=O)cc4C3=O)cc2)cc1
0.165,*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)cc1)C2=O
0.328,*C(=O)c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(=O)c4cccc(C(=O)c5ccc(Oc6ccc(N7C(=O)c8ccc(*)cc8C7=O)cc6)cc5)c4)cc3)cc1)C2=O
0.165,*c1ccc(Oc2cccc(Oc3ccc(N4C(=O)c5cccc(Oc6cccc(Oc7cccc8c7C(=O)N(*)C8=O)c6)c5C4=O)cc3)c2)cc1
0.224,*c1ccc(N2C(=O)c3ccc(C(=O)c4ccc5c(c4)C(=O)N(c4ccc(-c6nc7cc(-c8ccc9nc(-c%10ccccc%10)c(*)nc9c8)ccc7nc6-c6ccccc6)cc4)C5=O)cc3C2=O)cc1
0.151,*c1ccc2c(c1)C(=O)N(c1ccc(C(c3ccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)cc3)(C(F)(F)F)C(F)(F)F)cc1)C2=O
0.154,*c1ccc2c(c1)C(=O)N(c1cc(OCCN(CC)c3ccc(/N=N/c4ccc([N+](=O)[O-])cc4)cc3)cc(N3C(=O)c4ccc(C(*)(C(F)(F)F)C(F)(F)F)cc4C3=O)c1)C2=O
0.233,*c1ccc(N2C(=O)c3ccc(-c4cccc5c4C(=O)N(*)C5=O)cc3C2=O)cc1
0.143,*C1C(=O)N(c2ccccc2C(=O)OC)C(=O)C1*
0.237,*OC(=O)CCCCCC(*)=O
0.2385,*Oc1ccc(C(C)(C)c2ccc(OC(*)=O)cc2)cc1
0.206,*Oc1ccc(C(C)(c2ccccc2)c2ccc(OC(*)=O)cc2)cc1
0.219,*Oc1ccc(C(CCC)(CCC)c2ccc(OC(*)=O)cc2)cc1
0.241,*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CCCC2)cc1
0.185,*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)CCCCC2)cc1
0.183,*Oc1ccc(C2(c3ccc(OC(*)=O)c(C)c3)CCCCC2)cc1C
0.109,*Oc1c(Cl)cc(C2(c3cc(Cl)c(OC(*)=O)c(Cl)c3)CCCCC2)cc1Cl
0.165,*Oc1ccc(C2(c3ccc(OC(*)=O)c(Cl)c3)CCCCC2)cc1Cl
0.235,*Oc1ccc(C2(c3ccc(OC(*)=O)cc3)c3ccccc3-c3ccccc32)cc1
0.186,*CC(C)(C)CS(=O)(=O)CC(C)(C)COC(=O)O*
0.351,*CCN*
0.4005,*CCN(*)C(=O)CCCCCCCCCCCCCCCCC
0.354,*CCCN*
0.185,*c1nc(-c2ccccc2)nc(N(c2ccccc2)c2ncnc(N(*)c3ccccc3)n2)n1
0.219,*\N=C\c1ccc(C=Nc2c(cc(cc2-c2ccccc2)-c2ccc(cc2)-c2cc(-c3ccccc3)c(*)c(c2)-c2ccccc2)-c2ccccc2)cc1
0.21,*c1nc(C)nc(N(CCCCCCN(*)c2ccccc2)c2ccccc2)n1
0.389,*c1ccc(-c2nnc(-c3cccc(-c4nnc(*)n4-c4ccccc4)c3)n2-c2ccccc2)cc1
0.267,*Cc1ccc(*)cc1
0.101,*c1ccc(C(F)(F)C(*)(F)F)cc1
0.115,*c1ccc(C(Cl)(Cl)C(*)Cl)cc1
0.21,*CCCCCCCCCCc1ccc(-c2c(-c3ccccc3)cc(-c3cccc(-c4cc(-c5ccccc5)c(-c5ccc(*)cc5)c(-c5ccccc5)c4-c4ccccc4)c3)c(-c3ccccc3)c2-c2ccccc2)cc1
0.225,*c1ccc(C2C(C(=O)Oc3ccccc3)C(*)C2C(=O)Oc2ccccc2)cc1
0.258,*c1ccc(C2C(C#N)(C(=O)OCCC)C(*)C2(C#N)C(=O)OCCC)cc1
0.21,Cc1ccc(cc1)S(=O)(=O)OCCCCC(*)=C=C=C(*)CCCCOS(=O)(=O)c1ccc(C)cc1
0.26,CCNC(=O)OCCCCC(*)=C=C=C(*)CCCCOC(=O)NCC
0.338,*/C(=C(\c1ccccc1)c1ccc(*)cc1)c1ccccc1
0.194,*CCc1cc(*)c(C)cc1C
0.217,*CCc1ccc(*)c(C(C)=O)c1
0.245,*CCc1ccc(*)c(C(=O)OC)c1
0.406,*CCCCCC(*)CCCCCCCCCC
0.381,*CCCCCC(*)CCCCCCCCCCCC
0.403,*CCCCCC(*)CCCCCCCCCCCCCC
0.381,*CC(*)CCCCCCCCCCCCCCCCCC
0.403,*/C=C(/*)CCCCCCCCCCCCCCCCCCCCC(=O)O
0.41050000000000003,*/C=C(/*)C#CCCCCCCCCCCCCCCCCCCCCC(=O)O
0.3405,*/C=C/CCCCCCC(Cl)CCCCCC*
0.34199999999999997,*/C=C/CCCCCCCCCC(Cl)CCCCCCCCC*
0.31,*/C=C/CCCCCCCCCC(CCCCCCCCC*)COCCOCCOCCOCCOCCCCCC
0.39925,*CCCCCCCCCCCCCCCCCCCCC(*)COCCOCCOCCOCCOCCCCCC
0.332,*/C=C/CCCCCCCCCC(CCCCCCCCC*)COCCOCCOCCOCCOCCCCCCCCCCCCCC
0.38425,*CCCCCCCCCCCCCCCCCCCCC(*)COCCOCCOCCOCCOCCCCCCCCCCCCCC
0.3735,*CCCCCCCCCCCCCCC(*)Cl
0.468,*CCCCCCCCCCCCCCCCCCCCC(*)Cl
0.38049999999999995,*/C=C/CCCCCCCC*
0.446,*c1nc2cc(-c3ccc4[nH]c(-c5ccc(*)o5)nc4c3)ccc2[nH]1
0.398,*CCCCCCCCCCCCCCCCCCCCc1nnc(*)o1
0.222,*OC(=O)C(Cc1ccccc1)NC(=O)CCCCCCCCCCC(=O)NC(Cc1ccccc1)C(=O)OC1COC2C(*)COC12
0.1265,*C(F)(F)C(F)(F)C1(F)C(*)(F)OC(F)(F)C1(F)F
0.243,*c1ccc(Oc2ccc(-c3csc(/N=C/c4ccc(OCCOc5ccc(/C=N/c6nc(*)cs6)cc5OC)c(OC)c4)n3)cc2)cc1
0.218,*c1ccc(Oc2ccc(-c3csc(/N=C/c4ccc(OCCCCOc5ccc(/C=N/c6nc(*)cs6)cc5OC)c(OC)c4)n3)cc2)cc1
0.253,*c1ccc(Oc2ccc(-c3csc(/N=C/c4ccc(OCCCCCCOc5ccc(/C=N/c6nc(*)cs6)cc5OC)c(OC)c4)n3)cc2)cc1
0.228,*c1ccc(Oc2ccc(-c3csc(/N=C/c4ccc(OCCCCCCCCOc5ccc(/C=N/c6nc(*)cs6)cc5OC)c(OC)c4)n3)cc2)cc1
0.383,*c1ccc(Oc2ccc(-c3csc(/N=C/c4ccc(OCCCCCCCCCCOc5ccc(/C=N/c6nc(*)cs6)cc5OC)c(OC)c4)n3)cc2)cc1
0.259,*CC(=O)c1ccc(Oc2ccc(C(=O)COc3ccc(/C=C4\CC/C(=C\c5ccc(O*)cc5)C4=O)cc3)cc2)cc1
0.228,*CC(=O)c1ccc(Oc2ccc(C(=O)COc3ccc(/C=C4\CC/C(=C\c5ccc(O*)c(OC)c5)C4=O)cc3OC)cc2)cc1
0.296,*Oc1cccc(Oc2ccc(C(=O)Nc3ccc(Oc4ccc(NC(=O)c5ccc(*)cc5)cc4)cc3)cc2)c1C#N
0.44475000000000003,*c1sc(*)c2c1OCC(CCCCCCCCCCCCCCCC)O2
0.352,*CCCCCCCCCCCSCCCCCCCCCCS*
0.34125,*CCCCCCCCCCCCCCS*
0.41824999999999996,*c1cc(CCCCCCCCCCCCCC)c(*)s1
0.381,*c1cc(CCCCCCCCCCCCCCCC)c(*)s1
0.307,*c1sc(-c2cc(CCCCCCCCCC)c(*)s2)cc1CCCCCCCCCC
0.282,*CCCCCCCCCCSCCCCS*
0.36474999999999996,*c1nc2cc3sc(-c4cc(OCCCCCCCCCCCC)c(*)cc4OCCCCCCCCCCCC)nc3cc2s1
0.40475,*c1cc(CCCCCCCCCCCCC)c(*)s1
0.37424999999999997,*c1cc(CCCCCCCCCCCCCCC)c(*)s1
0.396,*c1cc(CCCCCCCCCCCCCCCCC)c(*)s1
0.34,*c1cc(CCCCCCCCCCCCCCCCCCC)c(*)s1
0.365,*/C=C/c1sc(*)c(OCCCCCCCCCCCC)c1OCCCCCCCCCCCC
0.39725,*c1cc(CCCCCCCCCCCCCCCCCCCCCC)c(*)s1
0.41300000000000003,*c1ccc(-c2sc(-c3cc(CCCCCCCCCCCC)c(*)s3)cc2CCCCCCCCCCCC)cc1
0.36525,*c1cc(CCCCCCCCCCCCCCCCCC)c(*)s1
0.368,*/C=C/c1cc(CCCCCCCCCCCC)c(*)s1
0.291,*/C=C/c1sc(/C=C/c2cc(CCCCCCCCCCCC)c(*)s2)cc1CCCCCCCCCCCC
0.299,*c1ccc(-c2ccc(-c3sc(*)c(CCCCCCCCCCCC)c3CCCCCCCCCCCC)s2)s1
0.34,*c1ccc(-c2sc(-c3cc(CCCCCCCCCCCC)c(*)s3)cc2CCCCCCCCCCCC)s1
0.349,*c1ccc(-c2sc(-c3cc(SCCCCCCCCCCCC)c(*)s3)cc2SCCCCCCCCCCCC)cc1
0.482,*c1sc(*)c2sc(CCCCCCCCC)nc12
0.37875,*c1sc(*)c(OCCCCCCCCCCCC)c1C
0.43675,*c1sc(*)c(OCCCCCCCCCCCCCC)c1C
0.388,*c1sc(*)c(OCCCCCCCCCCCCCCCC)c1C
0.37474999999999997,*c1sc(*)c(OCCCCCCCCCCCCCCCCCCCC)c1C
0.316,*c1cc(-c2sc(-c3cc(CCCCCCCCCCCCCC)c(*)s3)cc2CCCCCCCCCCCCCC)c2cccccc1-2
0.317,*CCCCCOC(=O)CCCCCNC(=O)O*
0.442,*CCCCCCCCCCCCCCCCOC(=O)CCCCCCCCC(=O)O*
0.307,*CCCCCCCCCC#CC#CCCCCCCCCCOC(=O)CCCCCCCCC(=O)O*
0.304,*CCCCCCCCCCCCCCCCOC(=O)CC/C=C/CCC(=O)O*
0.315,*CCCCCCCCCCCCCCCCOC(=O)C/C=C/CC(=O)O*
0.274,*CCCCCCCCCCCCOC(=O)CC/C=C/CCC(=O)O*
0.318,*CCCCCCCCCCCCCCOC(=O)CC/C=C/CCC(=O)O*
0.319,*CCCCCCCCCCOC(=O)CCCCCCCCCCC(=O)O*
0.297,*CCCCCCCCCCCCOC(=O)CCCCCCCCCCC(=O)O*
0.29400000000000004,*CCCCCCCCCCCCOC(=O)CCCCCCCCCCCCC(=O)O*
0.297,*CCCCCCCCCCCCCCCCOC(=O)CCCCCCCCCCCCC(=O)O*
0.49475,*CCCCOC(=O)CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC(=O)O*
0.387,*CCCCCCCCCCCCCCCCCCCCCCOC(=O)CCC(=O)O*
0.376,*CCCCCCCCCCCCCCCCCCCCCCOC(=O)CCCCC(=O)O*
0.367,*CCCCCCCCCCCCCCCCCCCCCCOC(=O)Cc1ccccc1CC(=O)O*
0.354,*CCCCCCCCCCCCCCCCCCCCCCOC(=O)CC(CC(=O)O*)c1ccccc1
0.389,*CCCCCCCCCCCCCCC(=O)N*
0.3315,*CCCCCCCCNC(=O)CCCCCCCCC(=O)NCCCCCCCCNC(=O)C(=O)N*
0.33,*CCCCCCN(C)C(=O)CCCCCCCCCCCCCCC(=O)N(*)C
0.333,*CCCCCCN(CC)C(=O)CCCCCCCCCCCCCCCCC(=O)N(*)CC
0.40800000000000003,*C(=O)N(*)CCCCCCCCCCCCCCCCCC
0.44,*CCCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCC(=O)N*
0.412,*CCNC(=O)CCCCCCCCCCCCCCC(=O)N*
0.3735,*CCCCNC(=O)CCCCCCCCCCCCCCC(=O)N*
0.367,*CCCCCCNC(=O)CCCCCCCCCCCCCCC(=O)N*
0.355,*CCCCCCCCNC(=O)CCCCCCCCCCCCCCC(=O)N*
0.368,*CCCCCCCCCCNC(=O)CCCCCCCCCCCCCCC(=O)N*
0.396,*CCCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCC(=O)N*
0.39949999999999997,*CCCCCCCCCCCCNC(=O)CCCCCCCCCCCCC(=O)N*
0.37175,*CCCCCCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCC(=O)N*
0.41374999999999995,*CCCCCCCNC(=O)CCCCCCCCCCCCCCCCCCC(=O)N*
0.34950000000000003,*CCCCCNC(=O)CCCCCCCCCCCCCCCCCCC(=O)N*
0.40700000000000003,*CCCNC(=O)CCCCCCCCCCCCCCCCCCC(=O)N*
0.389,*CCCCCCCCCCCNC(=O)CCCCCCCCCCC(=O)N*
0.359,*CCNC(=O)CCCCCCCCCCCCCCCCC(=O)N*
0.417,*Oc1ccc(/N=C/C=N/c2ccc(OC(=O)Nc3ccc(Cc4ccc(NC(*)=O)cc4)cc3)cc2)cc1
0.297,*Oc1ccc(/N=C/C=N/c2ccc(OC(=O)Nc3cc(NC(*)=O)ccc3C)cc2)cc1
0.349,*Oc1ccc(/N=C/C=N/c2ccc(OC(=O)NC3CC(C)(C)CC(C)(CNC(*)=O)C3)cc2)cc1
0.325,*Oc1ccc(/N=C/C=N/c2ccc(OC(=O)NCCCCCCNC(*)=O)cc2)cc1
0.326,*Oc1ccc(/N=C/CCC/C=N/c2ccc(OC(=O)Nc3ccc(Cc4ccc(NC(*)=O)cc4)cc3)cc2)cc1
0.252,*Oc1ccc(/N=C/CCC/C=N/c2ccc(OC(=O)Nc3cc(NC(*)=O)ccc3C)cc2)cc1
0.207,*Oc1ccc(/N=C/CCC/C=N/c2ccc(OC(=O)NC3CC(C)(C)CC(C)(CNC(*)=O)C3)cc2)cc1
0.294,*Oc1ccc(/N=C/CCC/C=N/c2ccc(OC(=O)NCCCCCCNC(*)=O)cc2)cc1
0.297,*C(=O)Nc1ccc(Oc2ccc(N3C(=O)c4ccc(*)cc4C3=O)cc2)cc1
0.111,*c1ccc2c(c1)C(=O)N(c1cccc(C(c3cccc(N4C(=O)c5ccc(C(*)(C(F)(F)F)C(F)(F)F)cc5C4=O)c3)(C(F)(F)F)C(F)(F)F)c1)C2=O
0.213,*c1ccc(Oc2ccc(C(c3ccc(Oc4ccc(-n5c(=O)c6cc7c(=O)n(*)c(=O)c7cc6c5=O)cc4C(F)(F)F)cc3)(C(F)(F)F)C(F)(F)F)cc2)c(C(F)(F)F)c1
0.152,*c1ccc2c(c1)C(=O)N(c1ccc(Oc3ccc(C(c4ccc(Oc5ccc(N6C(=O)c7ccc(C(*)(C(F)(F)F)C(F)(F)F)cc7C6=O)cc5C(F)(F)F)cc4)(C(F)(F)F)C(F)(F)F)cc3)c(C(F)(F)F)c1)C2=O
0.335,*CC(CCCCCCCCCCCCCCCC)C1C(=O)N(CCCCCCCCCCCC)C(=O)C1*
0.314,*Nc1ccc(*)cc1CCCCCCCCCCCCCCC
0.309,*CCN(*)C(=O)CCCCCCCCCCCCCC
0.33599999999999997,*CCN(*)C(=O)CCCCCCCCCCCCCCC
0.4075,*c1cc(CCCCCCCCCCCCCCCC)c(*)[nH]1
0.298,*CCCCCCCCCCN/C(C)=N/*
0.27949999999999997,*c1[nH]c(*)c(CC(=O)OCCCCCCCCCCCC)c1CC(=O)OCCCCCCCCCCCC
0.3671666666666667,*Nc1ccc(*)cc1OCCCCCCCCCCCCCCCC
0.408,*CCCCCCCCCCCCCCCC[N+](C)(C)CCCCCC[N+](*)(C)C
0.151,*OS(=O)(=O)c1cccc(S(=O)(=O)Oc2ccc(C3(c4ccc(*)cc4)CCCCC3)cc2)c1
0.356,*c1cc(CCCCCCCCCCCC)c(*)cc1CCCCCCCCCCCC
0.256,*c1cc(OCCCCCCCCCC)c(*)cc1OCCCCCCCCCC
0.33299999999999996,*/C=C/c1cc(OCCCCCCCCCCCCCCCC)c(/C=C/c2ccc(*)cc2)cc1OCCCCCCCCCCCCCCCC
0.368,*/C=C/c1cc(CCCCCCCCCCC)c(/C=C/c2ccc(*)cc2)cc1CCCCCCCCCCC
0.315,*/C=C/c1cc(CCCCCCCCCCCC)c(/C=C/c2ccc(*)cc2)cc1CCCCCCCCCCCC
0.196,*/C=C/c1ccc(*)c(-c2cc(OCC(CC)CCCC)c(OCC(CC)CCCC)cc2-c2ccccc2)c1
0.256,*/C=C/c1ccc(*)c(-c2cc(-c3ccccc3)c(OCC(CC)CCCC)cc2OCC(CC)CCCC)c1
0.237,*/C=C/c1ccc(*)c(-c2cc(OCC(CC)CCCC)c(-c3ccccc3)cc2OCC(CC)CCCC)c1
0.29,*c1ccc(-c2cc(-c3ccc(OCCCCCC)cc3)cc(-c3ccc(-c4ccc5c(c4)C(CCCCCC)(CCCCCC)c4cc(*)ccc4-5)cc3)c2-c2ccc(OCCCCCC)cc2)cc1
0.219,*/C=C/c1ccc(*)c(-c2ccc(OCC(CC)CCCC)c3ccccc23)c1
0.257,*/C=C/c1ccc(*)c(-c2c(OCC(CC)CCCC)ccc3ccccc23)c1
0.212,*/C=C/c1ccc(*)c(-c2c(OCC(CC)CCCC)ccc3cc(-c4ccccc4)ccc23)c1
0.236,*c1ccc(-c2ccc3c(c2)C(CCCCCCBr)(CCCCCCBr)c2cc(*)ccc2-3)cc1
0.487,*c1ccc(-c2ccc3c(c2)C(CCCCCCC#N)(CCCCCCC#N)c2cc(*)ccc2-3)cc1
0.308,*/C=C/c1ccc(*)c(-c2cc(OCCC(C)C)c(OCCC(C)C)cc2-c2ccc(OC)cc2)c1
0.184,*/C=C/c1ccc(*)c(-c2cc(OCCC(C)C)c(OCCC(C)C)cc2-c2ccc(C(F)(F)F)cc2)c1
0.334,*/C=C/c1ccc(*)c(-c2cc(OCCC(C)C)c(OCCC(C)C)cc2-c2ccc(F)cc2)c1
0.307,*/C=C/c1ccc(*)c(-c2cc(OCCC(C)C)c(OCCC(C)C)cc2-c2ccccc2)c1
0.252,*c1ccc(-c2ccc(C3(*)CCCCC3)cc2)cc1
0.247,*c1ccc(-c2ccc(C(*)(C)C)cc2)cc1
0.203,*c1ccc(-c2ccc(C3(*)c4ccccc4-c4ccccc43)cc2)cc1
0.141,*c1cc(*)c(O)c(/C=N/c2ccc(Cl)cc2)c1
0.258,*/C=C/c1cc(OCC2CC3CC2C2CCCC32)c(*)cc1OC
0.24,*/C=C/c1cc(OCC2CC3CC2C2CCCC32)c(*)cc1OCC1CC2CC1C1CCCC21
0.215,*c1cc(/C=N/c2ccc(C)cc2)c(*)c(OC)c1
0.183,*c1cc(O)c(O)cc1*
0.266,*c1cc(*)c(O)c(/C=N/c2ccc(N3CCOCC3)cc2)c1
0.376,*c1ccc(-c2ccc3c(c2)C(CCCCCC)(CCCCCC)c2cc(*)ccc2-3)cc1
0.46,*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3cc(CCCCCC)c(*)cc3CCCCCC)ccc1-2
0.349,*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3cc(CCCCCCCC)c(*)cc3CCCCCCCC)ccc1-2
0.216,*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3cc(CCCCCCCCCC)c(*)cc3CCCCCCCCCC)ccc1-2
0.31,*/C=C/c1cc(OCCCCCCCC)c(*)cc1OC
0.251,*/C=C/c1cc(OCCCCCCCCCCCC)c(*)cc1OC
0.192,*c1cc(/C=N/c2cccc(C)c2)cc(*)c1O
0.524,*/C=C/c1cc(OCCCCCC)c(*)cc1OC
0.309,*c1cc(OCCCCCCCCCCCC)c(*)cc1O
0.291,*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3cc(OCCCCCCCC)c(*)cc3OCCCCCCCC)ccc1-2
0.184,*c1ccc2c(c1)C(CCCCCC)(CCCCCC)c1cc(-c3cc(OCc4ccccc4)c(*)cc3OCc3ccccc3)ccc1-2
0.242,*/C=C/c1cc(OCC(CC)CCCC)c(*)cc1-c1ccc(N(c2ccc(OC)cc2)c2ccc(OC)cc2)cc1
0.198,*/C=C/c1cc(-c2ccc3c(c2)C(CCCCCCCC)(CCCCCCCC)c2ccccc2-3)c(*)cc1-c1ccc(F)cc1F
0.185,*/C=C/c1cc(OCC(CC)CCCC)c(*)cc1-c1ccc(F)c(C(F)(F)F)c1
0.236,*c1ccc(-c2ccc(-c3ccc(C(*)(CC)C(F)(F)F)cc3)cc2)cc1
0.233,*c1ccc(-c2ccc(C(*)(c3ccccc3)C(F)(F)F)cc2)cc1
0.278,*c1ccc(-c2ccc(-c3ccc(C(*)(c4ccccc4)C(F)(F)F)cc3)cc2)cc1
0.185,*c1ccc(-c2ccc(C(*)(c3ccc(F)cc3)C(F)(F)F)cc2)cc1
0.22,*c1ccc(-c2ccc(-c3ccc(C(*)(c4ccc(F)cc4)C(F)(F)F)cc3)cc2)cc1
0.172,*c1ccc(-c2ccc(-c3ccc(C(*)(c4c(F)c(F)c(F)c(F)c4F)C(F)(F)F)cc3)cc2)cc1
0.291,*c1ccc(-c2ccc(-c3ccc(C(*)c4c(F)c(F)c(F)c(F)c4F)cc3)cc2)cc1
0.285,*/C=C/c1cc(OCCc2ccccc2)c(*)cc1OC
0.194,*c1ccc(-c2ccc(C(*)(C)C(F)(F)F)cc2)cc1
1.59,*c1ccc2cc(*)ccc2c1
0.221,*c1ccc2ccc(*)cc2c1
0.571,*c1ccc(*)c2ccccc12
0.685,*c1cccc2c(*)cccc12
0.166,*CCc1c2ccccc2c(*)c2ccccc12
0.163,*c1cc2cccccc-2c1*
0.154,*CC(*)(F)C#N
0.339,*/C=C/c1ccc(*)c2ccc(CCCCCC)cc12
0.27,*/C=C/c1ccc(*)c2ccc(CCCCCCCCCCC)cc12
0.103,*c1c(-c2ccccc2)c(-c2ccccc2)c(*)c2cc(C(c3ccc(C#Cc4ccccc4)c(C#Cc4ccccc4)c3)(C(F)(F)F)C(F)(F)F)ccc12
0.16,*c1c(-c2ccccc2)c(-c2ccccc2)c(*)c2cc(Oc3ccc(C#Cc4ccccc4)c(C#Cc4ccccc4)c3)ccc12
0.224,*c1c(-c2ccccc2)c(-c2ccccc2)c(*)c2cc(-c3ccc(C#Cc4ccccc4)c(C#Cc4ccccc4)c3)ccc12
0.097,*CC(*)(F)C(=O)OCC(F)(F)C(F)(F)C(F)(F)F
0.095,*CC(*)(F)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
0.106,*CC(*)(F)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)F
0.108,*CC(*)(F)C(=O)OCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)F
0.121,*CC(*)(F)C(=O)OCCC(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)C(F)(F)F
0.109,*CC(F)(F)C1(F)CC(C(O)(C(F)(F)F)C(F)(F)F)CC1*
0.091,*c1c(*)c2ccccc2c2ccccc12
0.582,*c1ccc2ccc3c(*)cc(C#C)c4ccc1c2c34
0.429,*c1ccc2ccc3c(*)cc(C#CC=C)c4ccc1c2c34
0.314,*N1C(=O)C2=C(C=C(C=C2)C2=CC=C3C(=O)N(C(=O)C3=C2)C2=C3C=CC=C(*)C3=CC=C2)C1=O
0.231,CC1(C)CC(CC(C)(C*)C1)N1C(=O)C2=CC=C(C=C2C1=O)C1=CC2=C(C=C1)C(=O)N(*)C2=O
0.155,FC(F)(F)C(C1=CC2=C(OC(=N2)C2=CC=C(OC3=CC=C(OC4=CC=C(*)C=C4)C4=C3C3C5=C(C=CC=C5)C4C4=C3C=CC=C4)C=C2)C=C1)(C1=CC=C2OC(*)=NC2=C1)C(F)(F)F
0.109,FC(F)(F)C(C1=CC2=C(OC(=N2)C2=CC=C(OC3=C4C5C6=C(C=CC=C6)C(C6=C5C=CC=C6)C4=C(OC4=CC=C(*)C=C4)C4=C3C3C5=C(C=CC=C5)C4C4=C3C=CC=C4)C=C2)C=C1)(C1=CC=C2OC(*)=NC2=C1)C(F)(F)F
0.176,FC(F)(F)C(C1=CC2=C(OC(=N2)C2=CC=C(OC3=C4C5C6=C(C=CC=C6)C(C6=C5C=CC=C6)C4=C(OC4=CC=C(*)C=C4)C=C3)C=C2)C=C1)(C1=CC=C2OC(*)=NC2=C1)C(F)(F)F
0.252,*C1=NC2=CC(=CC=C2N1)C1=CC2=C(NC(O2)C2=CC=C(OC3=CC=C(OC4=CC=C(*)C=C4)C4=C3C3C5=C(C=CC=C5)C4C4=C3C=CC=C4)C=C2)C=C1
0.18,*C1=NC2=CC(=CC=C2N1)C1=CC2=C(NC(O2)C2=CC=C(OC3=C4C5C6=C(C=CC=C6)C(C6=C5C=CC=C6)C4=C(OC4=CC=C(*)C=C4)C4=C3C3C5=C(C=CC=C5)C4C4=C3C=CC=C4)C=C2)C=C1
